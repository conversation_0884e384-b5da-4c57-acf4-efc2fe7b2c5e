<?php

namespace App\Http\Controllers;

use App\Models\Link;
use App\Models\Config;
use App\Services\ChannelService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ChannelController extends Controller
{

    /**
     * Display the channel page
     */
    public function index(Request $request, ChannelService $channelService)
    {
        // Get token from query parameters
        $token = $request->route('token');
        $link = Link::query()
            ->where('token', $token)
            ->first();
        if (empty($link)) {
            return redirect()->route('home');
        }
        $config = $link->config;

        if (empty($config)) {
            return redirect()->route('home');
        }
        $channels = [];
        foreach ( $config->channel as $value) {
            $channels[] = [
                'id' => $value,
                'name' => $channelService->getChannelName($value)
            ];
        }

        return Inertia::render('Channel', [
            'channels' => $channels,
            'event' => $config->name
        ]);
    }

    /**
     * Get channel data by token (API endpoint)
     */
    public function getByToken(Request $request, $token)
    {
        $link = Link::getWithConfig($token);

        if (!$link) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        return response()->json([
            'link' => [
                'id' => $link->id,
                'token' => $link->token,
                'incident_id' => $link->incident_id,
                'created_at' => $link->created_at,
                'updated_at' => $link->updated_at,
            ],
            'config' => $link->config ? [
                'id' => $link->config->id,
                'name' => $link->config->name,
                'channel' => $link->config->channel,
                'incident_id' => $link->config->incident_id,
            ] : null,
        ]);
    }

    /**
     * Get config by incident_id
     */
    public function getConfig(Request $request, $incidentId)
    {
        $config = Config::where('incident_id', $incidentId)->first();

        if (!$config) {
            return response()->json(['error' => 'Config not found'], 404);
        }

        return response()->json([
            'config' => [
                'id' => $config->id,
                'name' => $config->name,
                'channel' => $config->channel,
                'incident_id' => $config->incident_id,
            ]
        ]);
    }
}
