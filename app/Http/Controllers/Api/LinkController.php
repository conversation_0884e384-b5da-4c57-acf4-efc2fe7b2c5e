<?php

namespace App\Http\Controllers\Api;

use App\Models\Link;
use Illuminate\Support\Str;

class LinkController
{

    public function create()
    {
        $configId = request('config_id');
        $passWord = request('password');
        if (empty($configId) || empty($passWord)) {
            return response([
                'status' => false,
                'message' => 'Missing config id or password'
            ]);
        }
        $link = Link::create([
            'incident_id' => $configId,
            'password' => $passWord,
            'token' => Str::uuid()->toString(),
        ]);

        return response([
            'status' => true,
            'link' => route('dashboard', ['token' => $link->token]),
            'token' => $link->token,
        ]);
    }
}
