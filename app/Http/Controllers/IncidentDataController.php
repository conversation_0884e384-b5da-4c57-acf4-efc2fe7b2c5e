<?php

namespace App\Http\Controllers;

use App\Models\IncidentData;
use App\Models\Link;
use Illuminate\Http\Request;

class IncidentDataController extends Controller
{
    /**
     * Get incident data by channel ID with pagination (no search/sort on backend)
     */
    public function getByChannelId(Request $request, $channelId)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 1000); // Allow larger limits for frontend sorting
        $token = $request->get('token');
        $link = Link::query()->where('token', $token)->first();
        if (empty($link)) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch incident data',
            ], 500);
        }
        try {
            $result = IncidentData::getByChannelId(
                $channelId,
                $link->incident_id,
                $page,
                $limit
            );
            return response()->json([
                'success' => true,
                'data' => $result['data'],
                'pagination' => [
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'hasMore' => $result['hasMore'],
                    'totalPages' => ceil($result['total'] / $result['limit'])
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch incident data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all incident data for a channel (for export or full data)
     */
    public function getAllByChannelId(Request $request, $channelId)
    {
        $search = $request->get('search');
        $sortBy = $request->get('sortBy', 'created');
        $sortDirection = $request->get('sortDirection', 'desc');

        try {
            $query = IncidentData::where('channel_id', $channelId);

            // Apply search if provided
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%")
                      ->orWhere('company', 'like', "%{$search}%")
                      ->orWhere('platform', 'like', "%{$search}%")
                      ->orWhere('source', 'like', "%{$search}%")
                      ->orWhere('name_incident', 'like', "%{$search}%");
                });
            }

            // Apply sorting
            $query->orderBy($sortBy, $sortDirection);

            $data = $query->get();

            return response()->json([
                'success' => true,
                'data' => $data,
                'total' => $data->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch all incident data',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
