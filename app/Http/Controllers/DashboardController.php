<?php

namespace App\Http\Controllers;

use App\Models\Link;
use App\Services\ChannelService;
use Inertia\Inertia;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the channel page
     */
    public function index(Request $request, ChannelService $channelService)
    {
        // Get token from query parameters
        $token = $request->route('token');
        $link = Link::query()
            ->where('token', $token)
            ->first();
        if (empty($link)) {
            return redirect()->route('home');
        }
        $config = $link->config;

        if (empty($config)) {
            return redirect()->route('home');
        }
        $channels = [];
        foreach ( $config->channel as $value) {
            $channels[] = [
                'id' => $value,
                'name' => $channelService->getChannelName($value)
            ];
        }

        return Inertia::render('Dashboard', [
            'channels' => $channels,
            'event' => $config->name
        ]);
    }

}
