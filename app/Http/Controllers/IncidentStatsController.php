<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\DB;
use App\Models\IncidentData;

class IncidentStatsController extends Controller
{
    /**
     * Get total posts count for a specific channel
     */
    public function getTotalPosts($channelId)
    {
        try {
            $totalPosts = IncidentData::where('channel_id', $channelId)
                ->where('id_config', Context::get('config_id'))
                ->count();
            return response()->json([
                'success' => true,
                'data' => [
                    'channel_id' => $channelId,
                    'total' => $totalPosts
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Lỗi khi lấy tổng số bài viết: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reaction flag statistics for a specific channel
     */
    public function getReactionStats($channelId)
    {
        try {
            $reactionStats = IncidentData::where('channel_id', $channelId)
                ->where('id_config', Context::get('config_id'))
                ->select('reaction_flag', DB::raw('count(*) as count'))
                ->whereNotNull('reaction_flag')
                ->where('reaction_flag', '!=', '')
                ->groupBy('reaction_flag')
                ->orderBy('count', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'label' => $item->reaction_flag,
                        'count' => $item->count
                    ];
                });
            return response()->json([
                'success' => true,
                'data' => $reactionStats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Lỗi khi lấy thống kê phản ứng: ' . $e->getMessage()
            ], 500);
        }
    }


}
