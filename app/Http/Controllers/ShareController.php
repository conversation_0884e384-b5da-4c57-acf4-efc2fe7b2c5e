<?php

namespace App\Http\Controllers;

use App\Models\Link;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class ShareController extends Controller
{
    // Sample passwords for demo (in real app, store in database)
    private function getSharePasswords()
    {
        return [
            'demo123' => Hash::make('password123'),
            'test456' => Hash::make('secret456'),
            'sample789' => Hash::make('demo789'),
        ];
    }

    /**
     * Verify password for share link
     */
    public function verifyPassword(Request $request, $token)
    {
        $request->validate([
            'password' => 'required|string'
        ]);
        $token = $request->route('token');
        $config = Link::query()->where('token', $token)->first();
        if ($request->password != $config->password) {
            return response()->json([
                'status' => false,
                'message' => 'Sai mật khẩu'
            ]);
        }
        $sessionKey = "share_link_verified_{$token}";
        Session::put($sessionKey, true);
        return response()->json(['success' => true]);
    }
}
