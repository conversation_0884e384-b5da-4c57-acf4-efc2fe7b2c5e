<?php

namespace App\Http\Middleware;

use App\Models\Link;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;

class ShareLinkAuth
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->route('token');
        if (!$token) {
            return redirect()->route('home');
        }
        $link = Link::query()->where('token', $token)->first();
        if (empty($link)){
            return redirect()->route('home');
        }
        $sessionKey = "share_link_verified_{$token}";
        Context::add('config_id', $link->incident_id);
        if (Session::has($sessionKey)) {
            return $next($request);
        }
        return Inertia::render('ShareLinkPasswordEntry', [
            'token' => $token,
            'returnUrl' => $request->fullUrl()
        ]);
    }
}
