<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class IncidentData extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'incident_data';

    protected $fillable = [
        'report_date',
        'created',
        'company',
        'platform',
        'channel_id',
        'source',
        'title',
        'content',
        'url_profile',
        'url_post',
        'es_id',
        'reaction_flag',
        'daily_like',
        'daily_share',
        'daily_comment',
        'accumulated_like',
        'accumulated_share',
        'accumulated_comment',
        'total',
        'id_config',
        'name_incident'
    ];

    protected $casts = [
        'report_date' => 'datetime',
        'created' => 'datetime',
        'daily_like' => 'integer',
        'daily_share' => 'integer',
        'daily_comment' => 'integer',
        'accumulated_like' => 'integer',
        'accumulated_share' => 'integer',
        'accumulated_comment' => 'integer',
        'total' => 'integer'
    ];

    /**
     * Get incident data by channel ID with pagination (simple version for frontend sorting)
     */
    public static function getByChannelId($channelId,$configId, $page = 1, $limit = 1000)
    {
        $query = static::where('channel_id', $channelId)
            ->where('id_config',$configId)
        ;
        // Get total count
        $total = $query->count();

        // Apply pagination
        $offset = ($page - 1) * $limit;
        $data = $query->orderBy('created', 'desc')
                     ->offset($offset)
                     ->limit($limit)
                     ->get();
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'hasMore' => ($offset + $limit) < $total
        ];
    }
}
