<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class LinkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample links data
        $links = [
            [
                'token' => 'abc123',
                'password' => Hash::make('password123'),
                'incident_id' => 'INC-2024-001',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'token' => 'def456',
                'password' => Hash::make('secret456'),
                'incident_id' => 'INC-2024-002',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'token' => 'ghi789',
                'password' => Hash::make('demo789'),
                'incident_id' => 'INC-2024-003',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('links')->insert($links);

        // Sample configs data (assuming the table exists in mysql2 connection)
        $configs = [
            [
                'incident_id' => 'INC-2024-001',
                'name' => 'Tech News Configuration',
                'channel' => 'technology',
            ],
            [
                'incident_id' => 'INC-2024-002',
                'name' => 'Business Reports Configuration',
                'channel' => 'business',
            ],
            [
                'incident_id' => 'INC-2024-003',
                'name' => 'Social Media Configuration',
                'channel' => 'social',
            ],
        ];

        // Insert into the mysql2 connection if it exists
        try {
            DB::connection('mysql2')->table('incident_report_configs')->insert($configs);
        } catch (\Exception $e) {
            // If mysql2 connection doesn't exist, just skip
            $this->command->info('Skipping mysql2 configs insertion: ' . $e->getMessage());
        }
    }
}
