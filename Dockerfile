# Sử dụng PHP 8.3
FROM php:8.3-fpm

# Cài đặt các package cần thiết và Node.js
RUN apt-get update && apt-get install -y \
        git unzip libpq-dev libzip-dev \
        zip libpng-dev libjpeg-dev libfreetype6-dev \
        libonig-dev supervisor cron \
        libicu-dev && \
        docker-php-ext-configure intl && \
        docker-php-ext-install intl gd pdo pdo_mysql zip mbstring sockets pcntl && \
        pecl install redis && docker-php-ext-enable redis && \
    # Cài Node.js (LTS)
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g yarn

# Cài đặt Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Thiết lập thư mục làm việc
WORKDIR /var/www

# Sao chép mã nguồn vào container
COPY . .

# Cài đặt Laravel dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Cài đặt frontend nếu cần
RUN yarn install && yarn build

# Phân quyền thư mục
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache

# Mở cổng 9000 cho PHP-FPM
EXPOSE 9000

# Khởi chạy PHP-FPM
CMD ["php-fpm"]
