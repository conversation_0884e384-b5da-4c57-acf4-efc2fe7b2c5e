<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

//Route::get('dashboard', function () {
//    return Inertia::render('Dashboard');
//})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware([
    'share.auth'
])->prefix('/{token?}')->group(function () {
    Route::get('dashboard',[\App\Http\Controllers\DashboardController::class,'index'])->name('dashboard');

    Route::get('channel', [App\Http\Controllers\ChannelController::class, 'index'])->name('channel');

    Route::get('channel/{channelId}', function ($token, $channelId) {
        return Inertia::render('ChannelDetail', [
            'channelId' => $channelId
        ]);
    })->name('channel.detail');

    Route::get('channel-token-demo', function () {
        return Inertia::render('ChannelTokenDemo');
    })->name('channel-token-demo');
});

// API routes for channel data
Route::prefix('api')->group(function () {
    Route::get('link/{token}', [App\Http\Controllers\ChannelController::class, 'getChannel'])->name('api.link.token');

    // Incident data routes
    Route::get('incident-data/channel/{channelId}', [App\Http\Controllers\IncidentDataController::class, 'getByChannelId'])->name('api.incident-data.channel');
    Route::get('incident-data/channel/{channelId}/all', [App\Http\Controllers\IncidentDataController::class, 'getAllByChannelId'])->name('api.incident-data.channel.all');
});

// Share link routes (with middleware protection)
Route::prefix('p')->group(function () {
    // Password verification route (no middleware)
    Route::post('{token}/verify', [App\Http\Controllers\ShareController::class, 'verifyPassword'])
        ->name('share.verify');

    // Protected share routes (with middleware)
    Route::middleware(['share.auth'])->group(function () {
        Route::get('{token}', function ($token) {
            // This is where you'd show your shared content
            // For demo, just show a simple page
            return Inertia::render('SharedContent', ['token' => $token]);
        })->name('share.show');
    });
});
