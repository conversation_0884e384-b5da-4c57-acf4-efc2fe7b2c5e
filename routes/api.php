<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
Route::any('/create-link',[App\Http\Controllers\Api\LinkController::class, 'create']);

// Incident Data Statistics Routes
Route::prefix('incident-data/stats')->middleware('share.api')->group(function () {
    Route::get('total-posts/{channelId}', [App\Http\Controllers\IncidentStatsController::class, 'getTotalPosts']);
    Route::get('reactions/{channelId}', [App\Http\Controllers\IncidentStatsController::class, 'getReactionStats']);
    Route::get('channel/{channelId}', [App\Http\Controllers\IncidentStatsController::class, 'getChannelStats']);
    Route::get('all-channels', [App\Http\Controllers\IncidentStatsController::class, 'getAllChannelsStats']);
});
