<script setup lang="ts">
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
</script>

<template>
    <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <!-- Title Skeleton -->
            <div class="h-4 w-24 bg-muted rounded animate-pulse"></div>
            <!-- Icon Skeleton -->
            <div class="h-8 w-8 bg-muted rounded-md animate-pulse"></div>
        </CardHeader>
        <CardContent>
            <div class="space-y-2">
                <!-- Value Skeleton -->
                <div class="h-8 w-16 bg-muted rounded animate-pulse"></div>
                <!-- Description Skeleton -->
                <div class="h-3 w-32 bg-muted rounded animate-pulse"></div>
            </div>
        </CardContent>
    </Card>
</template>
