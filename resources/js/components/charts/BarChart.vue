<script setup lang="ts">
import { type ChartDataPoint } from '@/types';
import { computed } from 'vue';

interface Props {
    data: ChartDataPoint[];
    title?: string;
    height?: number;
    showValues?: boolean;
    formatValue?: (value: number) => string;
}

const props = withDefaults(defineProps<Props>(), {
    height: 300,
    showValues: true,
    formatValue: (value: number) => value.toString()
});

const maxValue = computed(() => Math.max(...props.data.map(item => item.value)));

const chartData = computed(() => {
    return props.data.map((item, index) => ({
        ...item,
        percentage: (item.value / maxValue.value) * 100,
        color: item.color || `hsl(${index * 30}, 70%, 50%)`
    }));
});
</script>

<template>
    <div class="space-y-4">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        
        <div class="space-y-2">
            <div
                v-for="item in chartData"
                :key="item.label"
                class="space-y-1"
            >
                <div class="flex justify-between items-center text-sm">
                    <span class="font-medium">{{ item.label }}</span>
                    <span v-if="showValues" class="text-muted-foreground">
                        {{ formatValue(item.value) }}
                    </span>
                </div>
                
                <div class="w-full bg-muted rounded-full h-3 overflow-hidden">
                    <div
                        class="h-full rounded-full transition-all duration-500 ease-out"
                        :style="{
                            width: `${item.percentage}%`,
                            backgroundColor: item.color
                        }"
                    ></div>
                </div>
            </div>
        </div>
    </div>
</template>
