<script setup lang="ts">
import { type ChartDataPoint } from '@/types';
import { computed } from 'vue';

interface Props {
    data: ChartDataPoint[];
    title?: string;
    width?: number;
    height?: number;
    color?: string;
    formatValue?: (value: number) => string;
}

const props = withDefaults(defineProps<Props>(), {
    width: 400,
    height: 200,
    color: '#3b82f6',
    formatValue: (value: number) => value.toString()
});

const maxValue = computed(() => Math.max(...props.data.map(item => item.value)));
const minValue = computed(() => Math.min(...props.data.map(item => item.value)));
const valueRange = computed(() => maxValue.value - minValue.value);

const padding = 40;
const chartWidth = computed(() => props.width - padding * 2);
const chartHeight = computed(() => props.height - padding * 2);

const points = computed(() => {
    return props.data.map((item, index) => {
        const x = padding + (index / (props.data.length - 1)) * chartWidth.value;
        const y = padding + chartHeight.value - ((item.value - minValue.value) / valueRange.value) * chartHeight.value;
        return { x, y, ...item };
    });
});

const pathData = computed(() => {
    if (points.value.length === 0) return '';
    
    const path = points.value.reduce((acc, point, index) => {
        const command = index === 0 ? 'M' : 'L';
        return `${acc} ${command} ${point.x} ${point.y}`;
    }, '');
    
    return path.trim();
});

const areaPath = computed(() => {
    if (points.value.length === 0) return '';
    
    const path = pathData.value;
    const lastPoint = points.value[points.value.length - 1];
    const firstPoint = points.value[0];
    
    return `${path} L ${lastPoint.x} ${padding + chartHeight.value} L ${firstPoint.x} ${padding + chartHeight.value} Z`;
});
</script>

<template>
    <div class="space-y-4">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        
        <div class="relative">
            <svg :width="width" :height="height" class="border rounded-lg bg-background">
                <!-- Grid lines -->
                <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.1"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                
                <!-- Area fill -->
                <path
                    :d="areaPath"
                    :fill="color"
                    fill-opacity="0.1"
                />
                
                <!-- Line -->
                <path
                    :d="pathData"
                    fill="none"
                    :stroke="color"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
                
                <!-- Data points -->
                <circle
                    v-for="(point, index) in points"
                    :key="index"
                    :cx="point.x"
                    :cy="point.y"
                    r="4"
                    :fill="color"
                    class="hover:r-6 transition-all cursor-pointer"
                    :title="`${point.label}: ${formatValue(point.value)}`"
                />
                
                <!-- Y-axis labels -->
                <text
                    :x="padding - 10"
                    :y="padding + 5"
                    text-anchor="end"
                    class="text-xs fill-muted-foreground"
                >
                    {{ formatValue(maxValue) }}
                </text>
                <text
                    :x="padding - 10"
                    :y="padding + chartHeight + 5"
                    text-anchor="end"
                    class="text-xs fill-muted-foreground"
                >
                    {{ formatValue(minValue) }}
                </text>
            </svg>
            
            <!-- X-axis labels -->
            <div class="flex justify-between mt-2" :style="{ paddingLeft: `${padding}px`, paddingRight: `${padding}px` }">
                <span
                    v-for="point in points"
                    :key="point.label"
                    class="text-xs text-muted-foreground"
                >
                    {{ point.label }}
                </span>
            </div>
        </div>
    </div>
</template>
