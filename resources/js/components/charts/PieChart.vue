<script setup lang="ts">
import { type ChartDataPoint } from '@/types';
import { computed } from 'vue';

interface Props {
    data: ChartDataPoint[];
    title?: string;
    size?: number;
}

const props = withDefaults(defineProps<Props>(), {
    size: 200
});

const total = computed(() => props.data.reduce((sum, item) => sum + item.value, 0));

const chartData = computed(() => {
    let cumulativePercentage = 0;
    return props.data.map((item, index) => {
        const percentage = (item.value / total.value) * 100;
        const startAngle = cumulativePercentage * 3.6; // Convert to degrees
        const endAngle = (cumulativePercentage + percentage) * 3.6;
        
        cumulativePercentage += percentage;
        
        return {
            ...item,
            percentage: percentage.toFixed(1),
            startAngle,
            endAngle,
            color: item.color || `hsl(${index * 60}, 70%, 50%)`
        };
    });
});

const radius = computed(() => props.size / 2 - 10);
const center = computed(() => props.size / 2);

const createPath = (startAngle: number, endAngle: number) => {
    const start = polarToCartesian(center.value, center.value, radius.value, endAngle);
    const end = polarToCartesian(center.value, center.value, radius.value, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
        "M", center.value, center.value,
        "L", start.x, start.y,
        "A", radius.value, radius.value, 0, largeArcFlag, 0, end.x, end.y,
        "Z"
    ].join(" ");
};

const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
        x: centerX + (radius * Math.cos(angleInRadians)),
        y: centerY + (radius * Math.sin(angleInRadians))
    };
};
</script>

<template>
    <div class="space-y-4">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        
        <div class="flex items-center gap-6">
            <!-- Chart -->
            <div class="relative">
                <svg :width="size" :height="size" class="transform -rotate-90">
                    <path
                        v-for="(item, index) in chartData"
                        :key="index"
                        :d="createPath(item.startAngle, item.endAngle)"
                        :fill="item.color"
                        class="hover:opacity-80 transition-opacity cursor-pointer"
                        :title="`${item.label}: ${item.value} (${item.percentage}%)`"
                    />
                </svg>
                
                <!-- Center text -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-2xl font-bold">{{ total }}</div>
                        <div class="text-sm text-muted-foreground">Total</div>
                    </div>
                </div>
            </div>
            
            <!-- Legend -->
            <div class="space-y-2">
                <div
                    v-for="item in chartData"
                    :key="item.label"
                    class="flex items-center gap-2 text-sm"
                >
                    <div
                        class="w-3 h-3 rounded-full"
                        :style="{ backgroundColor: item.color }"
                    ></div>
                    <span class="font-medium">{{ item.label }}</span>
                    <span class="text-muted-foreground">({{ item.percentage }}%)</span>
                </div>
            </div>
        </div>
    </div>
</template>
