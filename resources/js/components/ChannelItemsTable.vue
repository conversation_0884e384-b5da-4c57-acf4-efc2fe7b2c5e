<script setup lang="ts">
import { type ChannelItem } from '@/types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Clock, User, Tag } from 'lucide-vue-next';

interface Props {
    items: ChannelItem[];
}

defineProps<Props>();

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank');
    }
};
</script>

<template>
    <div class="rounded-md border">
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead class="w-[50px]"></TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead class="hidden md:table-cell">Author</TableHead>
                    <TableHead class="hidden lg:table-cell">Published</TableHead>
                    <TableHead class="hidden lg:table-cell">Tags</TableHead>
                    <TableHead class="w-[100px]">Actions</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <TableRow v-for="item in items" :key="item.id" class="hover:bg-muted/50">
                    <TableCell>
                        <div 
                            v-if="item.thumbnail" 
                            class="w-10 h-10 rounded-md bg-cover bg-center"
                            :style="{ backgroundImage: `url(${item.thumbnail})` }"
                        ></div>
                        <div v-else class="w-10 h-10 rounded-md bg-muted flex items-center justify-center">
                            <ExternalLink class="h-4 w-4 text-muted-foreground" />
                        </div>
                    </TableCell>
                    <TableCell>
                        <div class="space-y-1">
                            <div class="font-medium leading-none">{{ item.title }}</div>
                            <div v-if="item.description" class="text-sm text-muted-foreground line-clamp-2">
                                {{ item.description }}
                            </div>
                        </div>
                    </TableCell>
                    <TableCell class="hidden md:table-cell">
                        <div v-if="item.author" class="flex items-center gap-1 text-sm">
                            <User class="h-3 w-3" />
                            <span>{{ item.author }}</span>
                        </div>
                        <span v-else class="text-muted-foreground text-sm">—</span>
                    </TableCell>
                    <TableCell class="hidden lg:table-cell">
                        <div class="flex items-center gap-1 text-sm text-muted-foreground">
                            <Clock class="h-3 w-3" />
                            <span>{{ formatDate(item.publishedAt) }}</span>
                        </div>
                    </TableCell>
                    <TableCell class="hidden lg:table-cell">
                        <div v-if="item.tags && item.tags.length > 0" class="flex flex-wrap gap-1">
                            <Badge 
                                v-for="tag in item.tags.slice(0, 2)" 
                                :key="tag" 
                                variant="outline" 
                                class="text-xs"
                            >
                                {{ tag }}
                            </Badge>
                            <Badge 
                                v-if="item.tags.length > 2" 
                                variant="outline" 
                                class="text-xs"
                            >
                                +{{ item.tags.length - 2 }}
                            </Badge>
                        </div>
                        <span v-else class="text-muted-foreground text-sm">—</span>
                    </TableCell>
                    <TableCell>
                        <Button 
                            v-if="item.url"
                            variant="ghost" 
                            size="sm" 
                            @click="openUrl(item.url)"
                            class="h-8 w-8 p-0"
                        >
                            <ExternalLink class="h-4 w-4" />
                        </Button>
                    </TableCell>
                </TableRow>
                <TableRow v-if="items.length === 0">
                    <TableCell :colspan="6" class="text-center py-8 text-muted-foreground">
                        No items found in this channel.
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
