<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-vue-next';

interface Props {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    selectedItemsPerPage: number | 'all';
    hasNextPage: boolean;
    hasPrevPage: boolean;
}

interface Emits {
    (e: 'goToPage', page: number): void;
    (e: 'nextPage'): void;
    (e: 'prevPage'): void;
    (e: 'goToFirstPage'): void;
    (e: 'goToLastPage'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
};

const goToPage = (page: number) => {
    emit('goToPage', page);
};

const nextPage = () => {
    emit('nextPage');
};

const prevPage = () => {
    emit('prevPage');
};

const goToFirstPage = () => {
    emit('goToFirstPage');
};

const goToLastPage = () => {
    emit('goToLastPage');
};

// Generate page numbers to show
const getVisiblePages = () => {
    const pages = [];
    const maxVisible = 5;

    if (props.totalPages <= maxVisible) {
        for (let i = 1; i <= props.totalPages; i++) {
            pages.push(i);
        }
    } else {
        // Always show first page
        pages.push(1);

        // Show pages around current page
        const start = Math.max(2, props.currentPage - 1);
        const end = Math.min(props.totalPages - 1, props.currentPage + 1);

        if (start > 2) {
            pages.push('...');
        }

        for (let i = start; i <= end; i++) {
            if (i !== 1 && i !== props.totalPages) {
                pages.push(i);
            }
        }

        if (end < props.totalPages - 1) {
            pages.push('...');
        }

        // Always show last page
        if (props.totalPages > 1) {
            pages.push(props.totalPages);
        }
    }

    return pages;
};
</script>

<template>
    <div v-if="totalPages > 1 && selectedItemsPerPage !== 'all'" class="flex items-center justify-between pt-4">
        <div class="text-sm text-muted-foreground">
            Trang {{ currentPage }} / {{ totalPages }} ({{ formatNumber(totalItems) }} mục)
        </div>

        <div class="flex items-center gap-2">
            <!-- First Page -->
            <Button
                variant="outline"
                size="sm"
                @click="goToFirstPage"
                :disabled="!hasPrevPage"
            >
                <ChevronsLeft class="h-4 w-4" />
            </Button>

            <!-- Previous Page -->
            <Button
                variant="outline"
                size="sm"
                @click="prevPage"
                :disabled="!hasPrevPage"
            >
                <ChevronLeft class="h-4 w-4" />
                Trước
            </Button>

            <!-- Page Numbers -->
            <div class="flex items-center gap-1">
                <template v-for="page in getVisiblePages()" :key="page">
                    <Button
                        v-if="typeof page === 'number'"
                        :variant="page === currentPage ? 'default' : 'outline'"
                        size="sm"
                        @click="goToPage(page)"
                        class="w-10"
                    >
                        {{ page }}
                    </Button>
                    <span v-else class="px-2 text-muted-foreground">{{ page }}</span>
                </template>
            </div>

            <!-- Next Page -->
            <Button
                variant="outline"
                size="sm"
                @click="nextPage"
                :disabled="!hasNextPage"
            >
                Tiếp
                <ChevronRight class="h-4 w-4" />
            </Button>

            <!-- Last Page -->
            <Button
                variant="outline"
                size="sm"
                @click="goToLastPage"
                :disabled="!hasNextPage"
            >
                <ChevronsRight class="h-4 w-4" />
            </Button>
        </div>
    </div>
</template>
