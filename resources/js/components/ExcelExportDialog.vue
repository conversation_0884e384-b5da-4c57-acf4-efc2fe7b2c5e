<script setup lang="ts">
import { computed } from 'vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import SimpleCheckbox from '@/components/SimpleCheckbox.vue';
import { Download, FileSpreadsheet, X, CheckSquare, Square } from 'lucide-vue-next';
import type { ExportColumn } from '@/composables/useExcelExport';

interface Props {
    isOpen: boolean;
    isExporting: boolean;
    exportColumns: ExportColumn[];
    channelName: string;
    totalItems: number;
}

interface Emits {
    (e: 'close'): void;
    (e: 'export', format: 'csv' | 'excel'): void;
    (e: 'toggleColumn', columnKey: string): void;
    (e: 'selectAll'): void;
    (e: 'deselectAll'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const selectedCount = computed(() =>
    props.exportColumns.filter(col => col.selected).length
);

const allSelected = computed(() =>
    props.exportColumns.length > 0 && selectedCount.value === props.exportColumns.length
);

const noneSelected = computed(() =>
    selectedCount.value === 0
);

const handleClose = () => {
    if (!props.isExporting) {
        emit('close');
    }
};

const handleExport = (format: 'csv' | 'excel') => {
    if (selectedCount.value === 0) return;
    emit('export', format);
};

const handleToggleColumn = (columnKey: string) => {
    emit('toggleColumn', columnKey);
};

const handleSelectAll = () => {
    if (allSelected.value) {
        emit('deselectAll');
    } else {
        emit('selectAll');
    }
};
</script>

<template>
    <!-- Overlay -->
    <div
        v-if="isOpen"
        class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        @click="handleClose"
    >
        <!-- Dialog -->
        <Card
            class="w-full max-w-2xl max-h-[90vh] overflow-hidden"
            @click.stop
        >
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-4">
                <div>
                    <CardTitle class="flex items-center gap-2">
                        <FileSpreadsheet class="h-5 w-5" />
                        Xuất Excel
                    </CardTitle>
                    <CardDescription>
                        Chọn các cột để xuất dữ liệu từ {{ channelName }}
                    </CardDescription>
                </div>
                <Button
                    variant="ghost"
                    size="sm"
                    @click="handleClose"
                    :disabled="isExporting"
                >
                    <X class="h-4 w-4" />
                </Button>
            </CardHeader>

            <CardContent class="space-y-6">
                <!-- Export Info -->
                <div class="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div class="text-sm">
                        <div class="font-medium">{{ totalItems }} dòng dữ liệu</div>
                        <div class="text-muted-foreground">{{ selectedCount }} / {{ exportColumns.length }} cột được chọn</div>
                    </div>
                    <Badge variant="outline">
                        {{ channelName }}
                    </Badge>
                </div>

                <!-- Column Selection -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-sm font-medium">Chọn cột để xuất</h3>
                        <Button
                            variant="outline"
                            size="sm"
                            @click="handleSelectAll"
                            :disabled="isExporting"
                        >
                            <component :is="allSelected ? CheckSquare : Square" class="mr-2 h-4 w-4" />
                            {{ allSelected ? 'Bỏ chọn tất cả' : 'Chọn tất cả' }}
                        </Button>
                    </div>

                    <div class="grid grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                        <div
                            v-for="column in exportColumns"
                            :key="column.key"
                            class="flex items-center space-x-2 p-2 rounded-md hover:bg-muted/50 transition-colors"
                        >
                            <SimpleCheckbox
                                :id="column.key"
                                :checked="column.selected"
                                @update:checked="() => handleToggleColumn(column.key)"
                                :disabled="isExporting"
                            />
                            <label
                                :for="column.key"
                                class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                            >
                                {{ column.label }}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="space-y-4">
                    <h3 class="text-sm font-medium">Định dạng xuất</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <!-- Excel Format -->
                        <Button
                            variant="outline"
                            class="h-auto p-4 flex flex-col items-center gap-2"
                            @click="handleExport('excel')"
                            :disabled="isExporting || noneSelected"
                        >
                            <FileSpreadsheet class="h-8 w-8 text-green-600" />
                            <div class="text-center">
                                <div class="font-medium">Excel (.xls)</div>
                                <div class="text-xs text-muted-foreground">Định dạng Excel với styling</div>
                            </div>
                        </Button>

                        <!-- CSV Format -->
                        <Button
                            variant="outline"
                            class="h-auto p-4 flex flex-col items-center gap-2"
                            @click="handleExport('csv')"
                            :disabled="isExporting || noneSelected"
                        >
                            <Download class="h-8 w-8 text-blue-600" />
                            <div class="text-center">
                                <div class="font-medium">CSV (.csv)</div>
                                <div class="text-xs text-muted-foreground">Dữ liệu thô, tương thích cao</div>
                            </div>
                        </Button>
                    </div>
                </div>

                <!-- Warning -->
                <div v-if="noneSelected" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="text-sm text-yellow-800">
                        ⚠️ Vui lòng chọn ít nhất một cột để xuất dữ liệu
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="isExporting" class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center gap-2 text-sm text-blue-800">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        Đang xuất dữ liệu...
                    </div>
                </div>
            </CardContent>
        </Card>
    </div>
</template>
