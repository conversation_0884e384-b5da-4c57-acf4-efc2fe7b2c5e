<script setup lang="ts">
import { type LandReport } from '@/types';
import { computed } from 'vue';
import EnhancedDataTable, { type TableColumn } from '@/components/EnhancedDataTable.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Eye, Calendar } from 'lucide-vue-next';

interface Props {
    reports: LandReport[];
    showActions?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showActions: true
});

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
};

const formatArea = (area: number) => {
    return `${area.toFixed(1)} ha`;
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

const getStatusVariant = (status: LandReport['status']) => {
    switch (status) {
        case 'available':
            return 'default';
        case 'sold':
            return 'secondary';
        case 'pending':
            return 'outline';
        case 'reserved':
            return 'destructive';
        default:
            return 'outline';
    }
};

const getLandTypeColor = (landType: LandReport['landType']) => {
    switch (landType) {
        case 'agricultural':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'residential':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'commercial':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'industrial':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        case 'forest':
            return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
};

// Define table columns
const columns = computed((): TableColumn<LandReport>[] => {
    const baseColumns: TableColumn<LandReport>[] = [
        {
            key: 'title',
            label: 'Property',
            sortable: true,
            searchable: true,
            width: '300px'
        },
        {
            key: 'location',
            label: 'Location',
            sortable: true,
            searchable: true,
            width: '200px'
        },
        {
            key: 'landType',
            label: 'Type',
            sortable: true,
            searchable: true,
            width: '120px'
        },
        {
            key: 'area',
            label: 'Area',
            sortable: true,
            align: 'right',
            width: '100px',
            sortFn: (a, b) => a.area - b.area
        },
        {
            key: 'value',
            label: 'Value',
            sortable: true,
            align: 'right',
            width: '150px',
            sortFn: (a, b) => a.value - b.value
        },
        {
            key: 'status',
            label: 'Status',
            sortable: true,
            searchable: true,
            width: '120px'
        },
        {
            key: 'lastUpdated',
            label: 'Last Updated',
            sortable: true,
            hideable: true,
            width: '150px',
            sortFn: (a, b) => new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime()
        }
    ];

    if (props.showActions) {
        baseColumns.push({
            key: 'actions',
            label: 'Actions',
            sortable: false,
            searchable: false,
            hideable: false,
            width: '100px',
            align: 'center'
        });
    }

    return baseColumns;
});

// Default sorting by last updated (newest first)
const defaultSort = { key: 'lastUpdated', direction: 'desc' as const };
</script>

<template>
    <EnhancedDataTable
        :data="reports"
        :columns="columns"
        :default-sort="defaultSort"
        search-placeholder="Search properties, locations, types..."
        empty-message="No land reports found."
    >
        <!-- Property column -->
        <template #cell-title="{ value, row }">
            <div class="space-y-1">
                <div class="font-medium">{{ value }}</div>
                <div v-if="row.description" class="text-sm text-muted-foreground line-clamp-1">
                    {{ row.description }}
                </div>
            </div>
        </template>

        <!-- Location column -->
        <template #cell-location="{ value }">
            <div class="flex items-center gap-1">
                <MapPin class="h-3 w-3 text-muted-foreground" />
                <span class="text-sm">{{ value }}</span>
            </div>
        </template>

        <!-- Land Type column -->
        <template #cell-landType="{ value }">
            <Badge :class="getLandTypeColor(value)" class="text-xs capitalize">
                {{ value }}
            </Badge>
        </template>

        <!-- Area column -->
        <template #cell-area="{ value }">
            <span class="text-sm font-medium">{{ formatArea(value) }}</span>
        </template>

        <!-- Value column -->
        <template #cell-value="{ value }">
            <span class="font-medium">{{ formatCurrency(value) }}</span>
        </template>

        <!-- Status column -->
        <template #cell-status="{ value }">
            <Badge :variant="getStatusVariant(value)" class="text-xs capitalize">
                {{ value }}
            </Badge>
        </template>

        <!-- Last Updated column -->
        <template #cell-lastUpdated="{ value }">
            <div class="flex items-center gap-1 text-sm text-muted-foreground">
                <Calendar class="h-3 w-3" />
                <span>{{ formatDate(value) }}</span>
            </div>
        </template>

        <!-- Actions column -->
        <template #cell-actions="{ row }">
            <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                <Eye class="h-4 w-4" />
            </Button>
        </template>
    </EnhancedDataTable>
</template>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
