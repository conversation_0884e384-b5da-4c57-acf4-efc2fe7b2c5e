<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Clock,
    ExternalLink,
    Heart,
    Share,
    MessageSquare,
    Calendar,
    Building,
    Globe,
    RefreshCw,
    TrendingUp
} from 'lucide-vue-next';
import { formatDate, formatNumber } from '@/utils/formatters';

interface LatestPost {
    id: number;
    title: string;
    content: string;
    url_post?: string;
    daily_like: number;
    daily_share: number;
    daily_comment: number;
    total: number;
    created: string;
    company?: string;
    platform?: string;
    source?: string;
    channel_name?: string;
}

interface Props {
    top10: any[];
    channels: any[];
}

const props = defineProps<Props>();

// Use the real data from props
const latestPosts = computed(() => props.top10 || []);

// Get channel name by ID
const getChannelName = (channelId: number) => {
    const channel = props.channels.find(c => c.id == channelId);
    return channel?.name || 'Unknown Channel';
};

// Format content for display
const getDisplayContent = (content: string, maxLength: number = 100) => {
    if (!content) return '';
    const cleanContent = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
    return cleanContent.length > maxLength
        ? cleanContent.substring(0, maxLength) + '...'
        : cleanContent;
};

// Get relative time
const getRelativeTime = (dateString: string) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
};

// Open URL in new tab
const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }
};

// Get platform color
const getPlatformColor = (platform?: string) => {
    switch (platform?.toLowerCase()) {
        case 'facebook': return 'bg-blue-100 text-blue-800';
        case 'twitter': return 'bg-sky-100 text-sky-800';
        case 'linkedin': return 'bg-blue-100 text-blue-800';
        case 'instagram': return 'bg-pink-100 text-pink-800';
        case 'youtube': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Card class="w-full">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
                <CardTitle class="flex items-center gap-2">
                    <TrendingUp class="h-5 w-5" />
                    Top 10 bài viết mới nhất
                </CardTitle>
                <CardDescription>
                    Các bài viết được đăng gần đây nhất trên tất cả kênh
                </CardDescription>
            </div>

        </CardHeader>

        <CardContent>
            <!-- Posts List -->
            <div v-if="latestPosts.length > 0" class="space-y-3">
                <div
                    v-for="(post, index) in latestPosts"
                    :key="post.id"
                    class="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                    <!-- Rank Number -->
                    <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span class="text-sm font-bold text-primary">{{ index + 1 }}</span>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0 space-y-2">
                        <!-- Title -->
                        <h3 class="font-medium text-sm leading-tight line-clamp-2">
                            {{ post.title }}
                        </h3>

                        <!-- Content Preview -->
                        <p class="text-xs text-muted-foreground line-clamp-2">
                            {{ getDisplayContent(post.content) }}
                        </p>

                        <!-- Metadata -->
                        <div class="flex items-center gap-2 text-xs text-muted-foreground">
                            <div class="flex items-center gap-1">
                                <Clock class="h-3 w-3" />
                                <span>{{ getRelativeTime(post.created) }}</span>
                            </div>

                            <span v-if="post.channel_id" class="flex items-center gap-1">
                                <Building class="h-3 w-3" />
                                {{ getChannelName(post.channel_id) }}
                            </span>
                        </div>

                        <!-- Tags -->
                        <div class="flex items-center gap-2 flex-wrap">
                            <Badge
                                v-if="post.platform"
                                variant="secondary"
                                class="text-xs"
                                :class="getPlatformColor(post.platform)"
                            >
                                {{ post.platform }}
                            </Badge>

                            <Badge v-if="post.source" variant="outline" class="text-xs">
                                {{ post.source }}
                            </Badge>
                        </div>
                    </div>

                    <!-- Stats & Actions -->
                    <div class="flex-shrink-0 text-right space-y-2">
                        <!-- Stats -->
                        <div class="flex items-center gap-3 text-xs text-muted-foreground">
                            <div class="flex items-center gap-1">
                                <Heart class="h-3 w-3 text-red-500" />
                                <span>{{ formatNumber(post.accumulated_like) }}</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <Share class="h-3 w-3 text-blue-500" />
                                <span>{{ formatNumber(post.accumulated_share) }}</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <MessageSquare class="h-3 w-3 text-green-500" />
                                <span>{{ formatNumber(post.accumulated_comment) }}</span>
                            </div>
                        </div>

                        <!-- Total -->
                        <div class="text-sm font-medium">
                            {{ formatNumber(post.total) }}
                        </div>

                        <!-- Action Button -->
                        <Button
                            v-if="post.url_post"
                            variant="ghost"
                            size="sm"
                            @click="openUrl(post.url_post)"
                            class="h-6 w-6 p-0"
                        >
                            <ExternalLink class="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-8 text-muted-foreground">
                <TrendingUp class="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Chưa có bài viết nào</p>
            </div>
        </CardContent>
    </Card>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
