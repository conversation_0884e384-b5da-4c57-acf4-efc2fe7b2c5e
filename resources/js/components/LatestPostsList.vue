<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Clock,
    ExternalLink,
    Heart,
    Share,
    MessageSquare,
    Calendar,
    Building,
    Globe,
    RefreshCw,
    TrendingUp
} from 'lucide-vue-next';
import { formatDate, formatNumber } from '@/utils/formatters';

interface LatestPost {
    id: number;
    title: string;
    content: string;
    url_post?: string;
    daily_like: number;
    daily_share: number;
    daily_comment: number;
    total: number;
    created: string;
    company?: string;
    platform?: string;
    source?: string;
    channel_name?: string;
}

interface Props {
    posts: any[];
}

const props = defineProps<Props>();

const isLoading = ref(false);
const error = ref('');
const latestPosts = ref<LatestPost[]>([]);

// Mock data for demonstration - replace with actual API call
const fetchLatestPosts = async () => {
    isLoading.value = true;
    error.value = '';

    try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data - replace with actual API endpoint
        const mockPosts: LatestPost[] = [
            {
                id: 1,
                title: "Công nghệ AI mới được phát triển tại Việt Nam",
                content: "Một công ty công nghệ Việt Nam vừa công bố phát triển thành công hệ thống AI tiên tiến...",
                url_post: "https://example.com/post1",
                daily_like: 1250,
                daily_share: 340,
                daily_comment: 89,
                total: 1679,
                created: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
                company: "VinTech",
                platform: "Facebook",
                source: "VnExpress",
                channel_name: "Tech News"
            },
            {
                id: 2,
                title: "Thị trường chứng khoán Việt Nam tăng mạnh",
                content: "VN-Index đã tăng 2.5% trong phiên giao dịch hôm nay, đạt mức cao nhất trong 3 tháng...",
                url_post: "https://example.com/post2",
                daily_like: 890,
                daily_share: 156,
                daily_comment: 67,
                total: 1113,
                created: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
                company: "SSI",
                platform: "LinkedIn",
                source: "CafeF",
                channel_name: "Finance"
            },
            {
                id: 3,
                title: "Startup Việt Nam nhận đầu tư 10 triệu USD",
                content: "Một startup fintech tại TP.HCM vừa hoàn tất vòng gọi vốn Series A với số tiền 10 triệu USD...",
                url_post: "https://example.com/post3",
                daily_like: 2100,
                daily_share: 445,
                daily_comment: 123,
                total: 2668,
                created: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
                company: "TechComBank",
                platform: "Twitter",
                source: "TechInAsia",
                channel_name: "Startup News"
            },
            {
                id: 4,
                title: "Giá xăng dầu tăng mạnh trong tuần này",
                content: "Giá xăng RON 95 tăng 800 đồng/lít, giá dầu diesel tăng 600 đồng/lít từ 15h chiều nay...",
                url_post: "https://example.com/post4",
                daily_like: 567,
                daily_share: 234,
                daily_comment: 156,
                total: 957,
                created: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
                company: "Petrolimex",
                platform: "Facebook",
                source: "Tuổi Trẻ",
                channel_name: "Economy"
            },
            {
                id: 5,
                title: "Đội tuyển Việt Nam thắng 3-0 trước Malaysia",
                content: "Trong trận đấu tại vòng loại World Cup 2026, đội tuyển Việt Nam đã có chiến thắng thuyết phục...",
                url_post: "https://example.com/post5",
                daily_like: 3400,
                daily_share: 890,
                daily_comment: 445,
                total: 4735,
                created: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
                company: "VFF",
                platform: "YouTube",
                source: "VTV",
                channel_name: "Sports"
            },
            {
                id: 6,
                title: "Thời tiết miền Bắc chuyển lạnh đột ngột",
                content: "Từ ngày mai, nhiệt độ miền Bắc sẽ giảm xuống 15-18 độ C, có nơi dưới 12 độ C...",
                url_post: "https://example.com/post6",
                daily_like: 234,
                daily_share: 67,
                daily_comment: 23,
                total: 324,
                created: new Date(Date.now() - 1000 * 60 * 150).toISOString(), // 2.5 hours ago
                company: "NCHMF",
                platform: "Facebook",
                source: "VnExpress",
                channel_name: "Weather"
            },
            {
                id: 7,
                title: "Lễ hội Tết Nguyên Đán 2024 tại Hà Nội",
                content: "Thành phố Hà Nội sẽ tổ chức nhiều hoạt động văn hóa đặc sắc trong dịp Tết Nguyên Đán...",
                url_post: "https://example.com/post7",
                daily_like: 1100,
                daily_share: 278,
                daily_comment: 89,
                total: 1467,
                created: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
                company: "UBND Hà Nội",
                platform: "Instagram",
                source: "Hà Nội Mới",
                channel_name: "Culture"
            },
            {
                id: 8,
                title: "Vaccine COVID-19 mới được phê duyệt",
                content: "Bộ Y tế vừa phê duyệt vaccine COVID-19 thế hệ mới với hiệu quả bảo vệ lên đến 95%...",
                url_post: "https://example.com/post8",
                daily_like: 1890,
                daily_share: 567,
                daily_comment: 234,
                total: 2691,
                created: new Date(Date.now() - 1000 * 60 * 210).toISOString(), // 3.5 hours ago
                company: "Bộ Y Tế",
                platform: "Facebook",
                source: "Sức Khỏe Đời Sống",
                channel_name: "Health"
            },
            {
                id: 9,
                title: "Dự án metro Hà Nội chính thức vận hành",
                content: "Tuyến metro số 3 Nhổn - Cầu Giấy chính thức đưa vào vận hành thương mại từ hôm nay...",
                url_post: "https://example.com/post9",
                daily_like: 2300,
                daily_share: 445,
                daily_comment: 167,
                total: 2912,
                created: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
                company: "MRB",
                platform: "LinkedIn",
                source: "Giao Thông Vận Tải",
                channel_name: "Transportation"
            },
            {
                id: 10,
                title: "Giá vàng trong nước tăng vọt lên 75 triệu/lượng",
                content: "Giá vàng miếng SJC tăng mạnh 500.000 đồng/lượng, đạt mức 75 triệu đồng/lượng...",
                url_post: "https://example.com/post10",
                daily_like: 1567,
                daily_share: 334,
                daily_comment: 123,
                total: 2024,
                created: new Date(Date.now() - 1000 * 60 * 270).toISOString(), // 4.5 hours ago
                company: "SJC",
                platform: "Facebook",
                source: "Đầu Tư",
                channel_name: "Investment"
            }
        ];

        latestPosts.value = mockPosts;
    } catch (err) {
        error.value = 'Không thể tải dữ liệu bài viết mới nhất';
        console.error('Error fetching latest posts:', err);
    } finally {
        isLoading.value = false;
    }
};

// Format content for display
const getDisplayContent = (content: string, maxLength: number = 100) => {
    if (!content) return '';
    const cleanContent = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
    return cleanContent.length > maxLength
        ? cleanContent.substring(0, maxLength) + '...'
        : cleanContent;
};

// Get relative time
const getRelativeTime = (dateString: string) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
};

// Open URL in new tab
const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }
};

// Get platform color
const getPlatformColor = (platform?: string) => {
    switch (platform?.toLowerCase()) {
        case 'facebook': return 'bg-blue-100 text-blue-800';
        case 'twitter': return 'bg-sky-100 text-sky-800';
        case 'linkedin': return 'bg-blue-100 text-blue-800';
        case 'instagram': return 'bg-pink-100 text-pink-800';
        case 'youtube': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

onMounted(() => {
    fetchLatestPosts();
});
</script>

<template>
    <Card class="w-full">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
                <CardTitle class="flex items-center gap-2">
                    <TrendingUp class="h-5 w-5" />
                    Top 10 bài viết mới nhất
                </CardTitle>
                <CardDescription>
                    Các bài viết được đăng gần đây nhất trên tất cả kênh
                </CardDescription>
            </div>
            <Button
                variant="outline"
                size="sm"
                @click="fetchLatestPosts"
                :disabled="isLoading"
            >
                <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': isLoading }" />
                Làm mới
            </Button>
        </CardHeader>

        <CardContent>
            <!-- Error Message -->
            <div v-if="error" class="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200 mb-4">
                {{ error }}
            </div>

            <!-- Loading State -->
            <div v-if="isLoading" class="space-y-4">
                <div v-for="n in 10" :key="n" class="animate-pulse">
                    <div class="flex space-x-4 p-4 border rounded-lg">
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                            <div class="flex space-x-2">
                                <div class="h-6 bg-gray-200 rounded w-16"></div>
                                <div class="h-6 bg-gray-200 rounded w-20"></div>
                            </div>
                        </div>
                        <div class="w-20 h-16 bg-gray-200 rounded"></div>
                    </div>
                </div>
            </div>

            <!-- Posts List -->
            <div v-else-if="latestPosts.length > 0" class="space-y-3">
                <div
                    v-for="(post, index) in latestPosts"
                    :key="post.id"
                    class="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                    <!-- Rank Number -->
                    <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span class="text-sm font-bold text-primary">{{ index + 1 }}</span>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0 space-y-2">
                        <!-- Title -->
                        <h3 class="font-medium text-sm leading-tight line-clamp-2">
                            {{ post.title }}
                        </h3>

                        <!-- Content Preview -->
                        <p class="text-xs text-muted-foreground line-clamp-2">
                            {{ getDisplayContent(post.content) }}
                        </p>

                        <!-- Metadata -->
                        <div class="flex items-center gap-2 text-xs text-muted-foreground">
                            <div class="flex items-center gap-1">
                                <Clock class="h-3 w-3" />
                                <span>{{ getRelativeTime(post.created) }}</span>
                            </div>

                            <span v-if="post.channel_name" class="flex items-center gap-1">
                                <Building class="h-3 w-3" />
                                {{ post.channel_name }}
                            </span>
                        </div>

                        <!-- Tags -->
                        <div class="flex items-center gap-2 flex-wrap">
                            <Badge
                                v-if="post.platform"
                                variant="secondary"
                                class="text-xs"
                                :class="getPlatformColor(post.platform)"
                            >
                                {{ post.platform }}
                            </Badge>

                            <Badge v-if="post.source" variant="outline" class="text-xs">
                                {{ post.source }}
                            </Badge>
                        </div>
                    </div>

                    <!-- Stats & Actions -->
                    <div class="flex-shrink-0 text-right space-y-2">
                        <!-- Stats -->
                        <div class="flex items-center gap-3 text-xs text-muted-foreground">
                            <div class="flex items-center gap-1">
                                <Heart class="h-3 w-3 text-red-500" />
                                <span>{{ formatNumber(post.daily_like) }}</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <Share class="h-3 w-3 text-blue-500" />
                                <span>{{ formatNumber(post.daily_share) }}</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <MessageSquare class="h-3 w-3 text-green-500" />
                                <span>{{ formatNumber(post.daily_comment) }}</span>
                            </div>
                        </div>

                        <!-- Total -->
                        <div class="text-sm font-medium">
                            {{ formatNumber(post.total) }}
                        </div>

                        <!-- Action Button -->
                        <Button
                            v-if="post.url_post"
                            variant="ghost"
                            size="sm"
                            @click="openUrl(post.url_post)"
                            class="h-6 w-6 p-0"
                        >
                            <ExternalLink class="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-8 text-muted-foreground">
                <TrendingUp class="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Chưa có bài viết nào</p>
            </div>
        </CardContent>
    </Card>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
