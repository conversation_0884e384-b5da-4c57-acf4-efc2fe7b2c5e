<script setup lang="ts">
import { type ChannelItem } from '@/types';
import { computed } from 'vue';
import EnhancedDataTable, { type TableColumn } from '@/components/EnhancedDataTable.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Clock, User } from 'lucide-vue-next';

interface Props {
    items: ChannelItem[];
}

const props = defineProps<Props>();

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank');
    }
};

// Define table columns
const columns = computed((): TableColumn<ChannelItem>[] => [
    {
        key: 'thumbnail',
        label: '',
        sortable: false,
        searchable: false,
        hideable: false,
        width: '60px'
    },
    {
        key: 'title',
        label: 'Title',
        sortable: true,
        searchable: true,
        width: '300px'
    },
    {
        key: 'author',
        label: 'Author',
        sortable: true,
        searchable: true,
        hideable: true,
        width: '150px'
    },
    {
        key: 'publishedAt',
        label: 'Published',
        sortable: true,
        hideable: true,
        width: '180px',
        sortFn: (a, b) => new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime()
    },
    {
        key: 'tags',
        label: 'Tags',
        sortable: false,
        searchable: true,
        hideable: true,
        width: '200px'
    },
    {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        searchable: false,
        hideable: false,
        width: '100px',
        align: 'center'
    }
]);

// Default sorting by published date (newest first)
const defaultSort = { key: 'publishedAt', direction: 'desc' as const };
</script>

<template>
    <EnhancedDataTable
        :data="items"
        :columns="columns"
        :default-sort="defaultSort"

        empty-message="No items found in this channel."
    >
        <!-- Thumbnail column -->
        <template #cell-thumbnail="{ row }">
            <div
                v-if="row.thumbnail"
                class="w-10 h-10 rounded-md bg-cover bg-center"
                :style="{ backgroundImage: `url(${row.thumbnail})` }"
            ></div>
            <div v-else class="w-10 h-10 rounded-md bg-muted flex items-center justify-center">
                <ExternalLink class="h-4 w-4 text-muted-foreground" />
            </div>
        </template>

        <!-- Title column -->
        <template #cell-title="{ value, row }">
            <div class="space-y-1">
                <div class="font-medium leading-none">{{ value }}</div>
                <div v-if="row.description" class="text-sm text-muted-foreground line-clamp-2">
                    {{ row.description }}
                </div>
            </div>
        </template>

        <!-- Author column -->
        <template #cell-author="{ value }">
            <div v-if="value" class="flex items-center gap-1 text-sm">
                <User class="h-3 w-3" />
                <span>{{ value }}</span>
            </div>
            <span v-else class="text-muted-foreground text-sm">—</span>
        </template>

        <!-- Published column -->
        <template #cell-publishedAt="{ value }">
            <div class="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock class="h-3 w-3" />
                <span>{{ formatDate(value) }}</span>
            </div>
        </template>

        <!-- Tags column -->
        <template #cell-tags="{ value }">
            <div v-if="value && value.length > 0" class="flex flex-wrap gap-1">
                <Badge
                    v-for="tag in value.slice(0, 2)"
                    :key="tag"
                    variant="outline"
                    class="text-xs"
                >
                    {{ tag }}
                </Badge>
                <Badge
                    v-if="value.length > 2"
                    variant="outline"
                    class="text-xs"
                >
                    +{{ value.length - 2 }}
                </Badge>
            </div>
            <span v-else class="text-muted-foreground text-sm">—</span>
        </template>

        <!-- Actions column -->
        <template #cell-actions="{ row }">
            <Button
                v-if="row.url"
                variant="ghost"
                size="sm"
                @click="openUrl(row.url)"
                class="h-8 w-8 p-0"
            >
                <ExternalLink class="h-4 w-4" />
            </Button>
        </template>
    </EnhancedDataTable>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
