<script setup lang="ts" generic="T extends Record<string, any>">
import { computed, ref, watch } from 'vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
    DropdownMenu, 
    DropdownMenuContent, 
    DropdownMenuCheckboxItem, 
    DropdownMenuTrigger,
    DropdownMenuLabel,
    DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
    ArrowUpDown, 
    ArrowUp, 
    ArrowDown, 
    Search, 
    Settings2, 
    X 
} from 'lucide-vue-next';

export interface TableColumn<T = any> {
    key: string;
    label: string;
    sortable?: boolean;
    searchable?: boolean;
    hideable?: boolean;
    width?: string;
    align?: 'left' | 'center' | 'right';
    render?: (value: any, row: T) => any;
    sortFn?: (a: T, b: T) => number;
}

interface Props {
    data: T[];
    columns: TableColumn<T>[];
    searchPlaceholder?: string;
    showSearch?: boolean;
    showColumnToggle?: boolean;
    defaultSort?: { key: string; direction: 'asc' | 'desc' };
    emptyMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
    searchPlaceholder: 'Search...',
    showSearch: true,
    showColumnToggle: true,
    emptyMessage: 'No data found.'
});

// Search functionality
const searchQuery = ref('');
const searchableColumns = computed(() => 
    props.columns.filter(col => col.searchable !== false)
);

// Column visibility
const hiddenColumns = ref<Set<string>>(new Set());
const visibleColumns = computed(() => 
    props.columns.filter(col => !hiddenColumns.value.has(col.key))
);

// Sorting functionality
const sortKey = ref<string | null>(props.defaultSort?.key || null);
const sortDirection = ref<'asc' | 'desc'>(props.defaultSort?.direction || 'asc');

const toggleSort = (columnKey: string) => {
    if (sortKey.value === columnKey) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        sortKey.value = columnKey;
        sortDirection.value = 'asc';
    }
};

const getSortIcon = (columnKey: string) => {
    if (sortKey.value !== columnKey) return ArrowUpDown;
    return sortDirection.value === 'asc' ? ArrowUp : ArrowDown;
};

// Data filtering and sorting
const filteredAndSortedData = computed(() => {
    let result = [...props.data];

    // Apply search filter
    if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase().trim();
        result = result.filter(row => {
            return searchableColumns.value.some(column => {
                const value = getNestedValue(row, column.key);
                return String(value).toLowerCase().includes(query);
            });
        });
    }

    // Apply sorting
    if (sortKey.value) {
        const column = props.columns.find(col => col.key === sortKey.value);
        if (column) {
            result.sort((a, b) => {
                if (column.sortFn) {
                    const sortResult = column.sortFn(a, b);
                    return sortDirection.value === 'asc' ? sortResult : -sortResult;
                }

                const aValue = getNestedValue(a, column.key);
                const bValue = getNestedValue(b, column.key);

                // Handle different data types
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return sortDirection.value === 'asc' ? aValue - bValue : bValue - aValue;
                }

                if (aValue instanceof Date && bValue instanceof Date) {
                    return sortDirection.value === 'asc' 
                        ? aValue.getTime() - bValue.getTime() 
                        : bValue.getTime() - aValue.getTime();
                }

                // String comparison
                const aStr = String(aValue).toLowerCase();
                const bStr = String(bValue).toLowerCase();
                
                if (sortDirection.value === 'asc') {
                    return aStr.localeCompare(bStr);
                } else {
                    return bStr.localeCompare(aStr);
                }
            });
        }
    }

    return result;
});

// Utility function to get nested object values
const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Column visibility controls
const toggleColumn = (columnKey: string) => {
    if (hiddenColumns.value.has(columnKey)) {
        hiddenColumns.value.delete(columnKey);
    } else {
        hiddenColumns.value.add(columnKey);
    }
};

const showAllColumns = () => {
    hiddenColumns.value.clear();
};

const hideAllColumns = () => {
    props.columns.forEach(col => {
        if (col.hideable !== false) {
            hiddenColumns.value.add(col.key);
        }
    });
};

// Clear search
const clearSearch = () => {
    searchQuery.value = '';
};

// Get cell alignment class
const getCellAlignClass = (align?: string) => {
    switch (align) {
        case 'center': return 'text-center';
        case 'right': return 'text-right';
        default: return 'text-left';
    }
};
</script>

<template>
    <div class="space-y-4">
        <!-- Controls -->
        <div class="flex items-center justify-between gap-4">
            <!-- Search -->
            <div v-if="showSearch" class="relative flex-1 max-w-sm">
                <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                    v-model="searchQuery"
                    :placeholder="searchPlaceholder"
                    class="pl-9 pr-9"
                />
                <Button
                    v-if="searchQuery"
                    variant="ghost"
                    size="sm"
                    class="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0"
                    @click="clearSearch"
                >
                    <X class="h-3 w-3" />
                </Button>
            </div>

            <!-- Column Toggle -->
            <DropdownMenu v-if="showColumnToggle">
                <DropdownMenuTrigger as-child>
                    <Button variant="outline" size="sm">
                        <Settings2 class="mr-2 h-4 w-4" />
                        Columns
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" class="w-48">
                    <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem
                        v-for="column in columns.filter(col => col.hideable !== false)"
                        :key="column.key"
                        :checked="!hiddenColumns.has(column.key)"
                        @click="toggleColumn(column.key)"
                    >
                        {{ column.label }}
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem @click="showAllColumns">
                        Show All
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem @click="hideAllColumns">
                        Hide All
                    </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>

        <!-- Results Info -->
        <div class="flex items-center justify-between text-sm text-muted-foreground">
            <div>
                Showing {{ filteredAndSortedData.length }} of {{ data.length }} results
                <span v-if="searchQuery"> for "{{ searchQuery }}"</span>
            </div>
            <div v-if="sortKey" class="flex items-center gap-1">
                <span>Sorted by {{ columns.find(col => col.key === sortKey)?.label }}</span>
                <component :is="getSortIcon(sortKey)" class="h-3 w-3" />
            </div>
        </div>

        <!-- Table -->
        <div class="rounded-md border">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead
                            v-for="column in visibleColumns"
                            :key="column.key"
                            :class="[
                                getCellAlignClass(column.align),
                                column.sortable !== false ? 'cursor-pointer select-none hover:bg-muted/50' : '',
                                column.width ? `w-[${column.width}]` : ''
                            ]"
                            @click="column.sortable !== false ? toggleSort(column.key) : null"
                        >
                            <div class="flex items-center gap-2">
                                <span>{{ column.label }}</span>
                                <component
                                    v-if="column.sortable !== false"
                                    :is="getSortIcon(column.key)"
                                    class="h-4 w-4"
                                    :class="sortKey === column.key ? 'text-foreground' : 'text-muted-foreground'"
                                />
                            </div>
                        </TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow
                        v-for="(row, index) in filteredAndSortedData"
                        :key="index"
                        class="hover:bg-muted/50"
                    >
                        <TableCell
                            v-for="column in visibleColumns"
                            :key="column.key"
                            :class="getCellAlignClass(column.align)"
                        >
                            <slot
                                :name="`cell-${column.key}`"
                                :value="getNestedValue(row, column.key)"
                                :row="row"
                                :column="column"
                            >
                                <span v-if="column.render">
                                    <component
                                        :is="column.render(getNestedValue(row, column.key), row)"
                                    />
                                </span>
                                <span v-else>
                                    {{ getNestedValue(row, column.key) }}
                                </span>
                            </slot>
                        </TableCell>
                    </TableRow>
                    <TableRow v-if="filteredAndSortedData.length === 0">
                        <TableCell :colspan="visibleColumns.length" class="text-center py-8 text-muted-foreground">
                            {{ emptyMessage }}
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </div>
</template>
