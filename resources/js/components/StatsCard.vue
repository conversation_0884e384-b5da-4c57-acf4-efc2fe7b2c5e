<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { LucideIcon } from 'lucide-vue-next';

interface Props {
    title: string;
    value: string | number;
    description?: string;
    icon?: LucideIcon;
    trend?: {
        value: number;
        isPositive: boolean;
    };
    color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray';
}

const props = withDefaults(defineProps<Props>(), {
    color: 'blue'
});

const colorClasses = {
    blue: 'text-blue-600 bg-blue-50 dark:bg-blue-950 dark:text-blue-400',
    green: 'text-green-600 bg-green-50 dark:bg-green-950 dark:text-green-400',
    red: 'text-red-600 bg-red-50 dark:bg-red-950 dark:text-red-400',
    yellow: 'text-yellow-600 bg-yellow-50 dark:bg-yellow-950 dark:text-yellow-400',
    purple: 'text-purple-600 bg-purple-50 dark:bg-purple-950 dark:text-purple-400',
    gray: 'text-gray-600 bg-gray-50 dark:bg-gray-950 dark:text-gray-400'
};
</script>

<template>
    <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">{{ title }}</CardTitle>
            <div
                v-if="icon"
                :class="[colorClasses[color], 'h-8 w-8 rounded-md flex items-center justify-center']"
            >
                <component :is="icon" class="h-4 w-4" />
            </div>
        </CardHeader>
        <CardContent>
            <div class="space-y-1">
                <div class="text-2xl font-bold">{{ value }}</div>
                <div class="flex items-center gap-2">
                    <p v-if="description" class="text-xs text-muted-foreground">
                        {{ description }}
                    </p>
                    <Badge
                        v-if="trend"
                        :variant="trend.isPositive ? 'default' : 'destructive'"
                        class="text-xs"
                    >
                        {{ trend.isPositive ? '+' : '' }}{{ trend.value }}%
                    </Badge>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
