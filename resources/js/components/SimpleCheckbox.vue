<script setup lang="ts">
import { Check } from 'lucide-vue-next';

interface Props {
    checked?: boolean;
    disabled?: boolean;
    id?: string;
}

interface Emits {
    (e: 'update:checked', checked: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
    checked: false,
    disabled: false
});

const emit = defineEmits<Emits>();

const handleClick = () => {
    if (!props.disabled) {
        emit('update:checked', !props.checked);
    }
};
</script>

<template>
    <div 
        :id="id"
        class="relative inline-flex items-center justify-center w-4 h-4 border border-gray-300 rounded cursor-pointer transition-all duration-200 hover:border-gray-400"
        :class="{
            'bg-blue-600 border-blue-600': checked,
            'bg-white': !checked,
            'opacity-50 cursor-not-allowed': disabled,
            'hover:bg-blue-700': checked && !disabled
        }"
        @click="handleClick"
    >
        <Check 
            v-if="checked" 
            class="w-3 h-3 text-white" 
        />
    </div>
</template>
