<script setup lang="ts">
import { TableHead } from '@/components/ui/table';
import { ArrowUpDown, ArrowUp, ArrowDown, GripVertical, GripHorizontal } from 'lucide-vue-next';
import type { TableColumn } from '@/config/tableColumns';

interface Props {
    column: TableColumn;
    sortKey: string;
    sortDirection: 'asc' | 'desc';
    width: number;
    isResizing: boolean;
}

interface Emits {
    (e: 'sort', columnKey: string): void;
    (e: 'startResize', event: MouseEvent, columnKey: string): void;
    (e: 'dragStart', event: DragEvent, columnKey: string): void;
    (e: 'dragOver', event: DragEvent, columnKey: string): void;
    (e: 'dragEnter', event: DragEvent, columnKey: string): void;
    (e: 'dragLeave', event: DragEvent): void;
    (e: 'drop', event: DragEvent, columnKey: string): void;
    (e: 'dragEnd', event: DragEvent): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const getSortIcon = () => {
    if (props.sortKey !== props.column.key) return ArrowUpDown;
    return props.sortDirection === 'asc' ? ArrowUp : ArrowDown;
};

const handleSort = () => {
    if (props.column.sortable) {
        emit('sort', props.column.key);
    }
};

const handleResizeStart = (event: MouseEvent) => {
    if (props.column.resizable) {
        emit('startResize', event, props.column.key);
    }
};

// Drag and drop handlers
const handleDragStart = (event: DragEvent) => {
    if (props.column.moveable) {
        emit('dragStart', event, props.column.key);
    }
};

const handleDragOver = (event: DragEvent) => {
    emit('dragOver', event, props.column.key);
};

const handleDragEnter = (event: DragEvent) => {
    emit('dragEnter', event, props.column.key);
};

const handleDragLeave = (event: DragEvent) => {
    emit('dragLeave', event);
};

const handleDrop = (event: DragEvent) => {
    emit('drop', event, props.column.key);
};

const handleDragEnd = (event: DragEvent) => {
    emit('dragEnd', event);
};
</script>

<template>
    <TableHead
        :class="[
            'relative select-none group',
            column.sortable ? 'cursor-pointer hover:bg-muted/50' : '',
            column.moveable ? 'cursor-move' : '',
            isResizing ? 'bg-muted/50' : ''
        ]"
        :style="{ width: `${width}px`, minWidth: `${width}px`, maxWidth: `${width}px` }"
        :draggable="column.moveable"
        @click="handleSort"
        @dragstart="handleDragStart"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        @dragend="handleDragEnd"
    >
        <div class="flex items-center justify-between pr-2">
            <!-- Drag Handle -->
            <div
                v-if="column.moveable"
                class="flex items-center mr-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
                @mousedown.stop
            >
                <GripHorizontal class="h-3 w-3 text-muted-foreground" />
            </div>

            <div class="flex items-center gap-2 flex-1 min-w-0">
                <span class="truncate">{{ column.label }}</span>
                <component
                    v-if="column.sortable"
                    :is="getSortIcon()"
                    class="h-4 w-4 flex-shrink-0"
                    :class="sortKey === column.key ? 'text-foreground' : 'text-muted-foreground'"
                />
            </div>

            <!-- Resize Handle -->
            <div
                v-if="column.resizable"
                class="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-blue-500 transition-colors group"
                @mousedown="handleResizeStart"
                @click.stop
            >
                <div class="absolute right-0 top-1/2 -translate-y-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <GripVertical class="h-4 w-4 text-blue-500" />
                </div>
            </div>
        </div>
    </TableHead>
</template>

<style scoped>
/* Ensure the resize handle is always on top */
.cursor-col-resize {
    z-index: 10;
}

/* Visual feedback during resize */
.cursor-col-resize:hover {
    background: linear-gradient(to right, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
}
</style>
