<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { type Channel } from '@/types';
import {
    ArrowDown,
    ArrowUp,
    ArrowUpDown,
    Building,
    Calendar,
    ExternalLink,
    Eye,
    Heart,
    MessageSquare,
    RefreshCw,
    Share,
} from 'lucide-vue-next';

// Components
import ContentEditor from '@/components/ContentEditor.vue';
import TableControls from '@/components/TableControls.vue';
import PaginationControls from '@/components/PaginationControls.vue';

// Composables and utilities
import { useIncidentDataTable } from '@/composables/useIncidentDataTable';
import { incidentDataColumns } from '@/config/tableColumns';
import { formatDate, formatNumber, openUrl } from '@/utils/formatters';

interface Props {
    channel: Channel;
}

const props = defineProps<Props>();

// Use the table composable
const {
    incidentData,
    isLoading,
    error,
    searchQuery,
    sortKey,
    sortDirection,
    selectedItemsPerPage,
    itemsPerPageOptions,
    currentPage,
    hiddenColumns,
    totalItems,
    totalPages,
    hasNextPage,
    hasPrevPage,
    startItem,
    endItem,
    fetchData,
    refreshData,
    clearSearch,
    toggleSort,
    changeItemsPerPage,
    goToPage,
    nextPage,
    prevPage,
    goToFirstPage,
    goToLastPage,
    toggleColumn,
    showAllColumns,
    hideAllColumns,
    startEditingContent,
    stopEditingContent,
    isEditingContent,
} = useIncidentDataTable();

// Computed properties
const visibleColumns = computed(() => 
    incidentDataColumns.filter(col => !hiddenColumns.value.has(col.key))
);

const getSortIcon = (columnKey: string) => {
    if (sortKey.value !== columnKey) return ArrowUpDown;
    return sortDirection.value === 'asc' ? ArrowUp : ArrowDown;
};

// Click outside handler for content editing
const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.content-editor')) {
        stopEditingContent();
    }
};

// Lifecycle
onMounted(() => {
    fetchData(props.channel.id);
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

// Content editing handlers
const handleContentSave = (itemId: number, newContent: string) => {
    // In a real app, you would save to backend here
    console.log('Saving content for item', itemId, ':', newContent);
    stopEditingContent();
};

const handleContentCancel = () => {
    stopEditingContent();
};

const handleStartContentEdit = (itemId: number) => {
    startEditingContent(itemId);
};
</script>

<template>
    <Card class="w-full">
        <CardHeader>
            <div class="flex items-center justify-between">
                <div>
                    <CardTitle class="flex items-center gap-2">
                        <Building class="h-5 w-5" />
                        {{ channel.name }}
                    </CardTitle>
                    <CardDescription>
                        Channel ID: {{ channel.id }}
                        <span v-if="channel.token" class="ml-2">
                            • Token: <code class="bg-muted px-1 rounded text-xs">{{ channel.token }}</code>
                        </span>
                    </CardDescription>
                </div>
                <Badge variant="outline">
                    {{ formatNumber(totalItems) }} items
                </Badge>
            </div>
        </CardHeader>
        
        <CardContent class="space-y-4">
            <!-- Table Controls -->
            <TableControls
                :search-query="searchQuery"
                :selected-items-per-page="selectedItemsPerPage"
                :items-per-page-options="itemsPerPageOptions"
                :hidden-columns="hiddenColumns"
                :columns="incidentDataColumns"
                :is-loading="isLoading"
                @update:search-query="searchQuery = $event"
                @update:selected-items-per-page="changeItemsPerPage"
                @clear-search="clearSearch"
                @refresh="refreshData(channel.id)"
                @toggle-column="toggleColumn"
                @show-all-columns="showAllColumns"
                @hide-all-columns="hideAllColumns"
            />

            <!-- Results Info -->
            <div class="flex items-center justify-between text-sm text-muted-foreground">
                <div>
                    Showing {{ startItem }} - {{ endItem }} of {{ formatNumber(totalItems) }} results
                    <span v-if="searchQuery"> for "{{ searchQuery }}"</span>
                    <span v-if="totalPages > 1" class="ml-2">
                        • Page {{ currentPage }} of {{ totalPages }}
                    </span>
                </div>
                <div v-if="sortKey" class="flex items-center gap-1">
                    <span>Sorted by {{ incidentDataColumns.find(col => col.key === sortKey)?.label }}</span>
                    <component :is="getSortIcon(sortKey)" class="h-3 w-3" />
                </div>
            </div>

            <!-- Error Message -->
            <div v-if="error" class="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
                {{ error }}
            </div>

            <!-- Table -->
            <div class="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead
                                v-for="column in visibleColumns"
                                :key="column.key"
                                :class="[
                                    column.sortable ? 'cursor-pointer select-none hover:bg-muted/50' : '',
                                    column.width ? `w-[${column.width}]` : ''
                                ]"
                                @click="column.sortable ? toggleSort(column.key) : null"
                            >
                                <div class="flex items-center gap-2">
                                    <span>{{ column.label }}</span>
                                    <component
                                        v-if="column.sortable"
                                        :is="getSortIcon(column.key)"
                                        class="h-4 w-4"
                                        :class="sortKey === column.key ? 'text-foreground' : 'text-muted-foreground'"
                                    />
                                </div>
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow
                            v-for="item in incidentData"
                            :key="item.id"
                            class="hover:bg-muted/50"
                        >
                            <!-- ID -->
                            <TableCell v-if="!hiddenColumns.has('id')" class="font-mono text-xs">
                                {{ item.id }}
                            </TableCell>
                            
                            <!-- Title with Content Editor -->
                            <TableCell v-if="!hiddenColumns.has('title')" class="max-w-xs">
                                <div class="space-y-2">
                                    <div class="font-medium break-words whitespace-normal">
                                        {{ item.title }}
                                    </div>
                                    <ContentEditor
                                        v-if="item.content"
                                        :model-value="item.content"
                                        :is-editing="isEditingContent(item.id)"
                                        :item-id="item.id"
                                        @save="handleContentSave(item.id, $event)"
                                        @cancel="handleContentCancel"
                                        @start-edit="handleStartContentEdit(item.id)"
                                    />
                                </div>
                            </TableCell>
                            
                            <!-- Company -->
                            <TableCell v-if="!hiddenColumns.has('company')" class="text-sm">
                                {{ item.company || '—' }}
                            </TableCell>
                            
                            <!-- Platform -->
                            <TableCell v-if="!hiddenColumns.has('platform')">
                                <Badge variant="outline" class="text-xs">
                                    {{ item.platform || '—' }}
                                </Badge>
                            </TableCell>
                            
                            <!-- Source -->
                            <TableCell v-if="!hiddenColumns.has('source')" class="text-sm">
                                {{ item.source || '—' }}
                            </TableCell>
                            
                            <!-- Daily Likes -->
                            <TableCell v-if="!hiddenColumns.has('daily_like')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <Heart class="h-3 w-3 text-red-500" />
                                    <span class="text-sm">{{ formatNumber(item.daily_like) }}</span>
                                </div>
                            </TableCell>
                            
                            <!-- Daily Shares -->
                            <TableCell v-if="!hiddenColumns.has('daily_share')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <Share class="h-3 w-3 text-blue-500" />
                                    <span class="text-sm">{{ formatNumber(item.daily_share) }}</span>
                                </div>
                            </TableCell>
                            
                            <!-- Daily Comments -->
                            <TableCell v-if="!hiddenColumns.has('daily_comment')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <MessageSquare class="h-3 w-3 text-green-500" />
                                    <span class="text-sm">{{ formatNumber(item.daily_comment) }}</span>
                                </div>
                            </TableCell>
                            
                            <!-- Total -->
                            <TableCell v-if="!hiddenColumns.has('total')" class="text-right font-medium">
                                {{ formatNumber(item.total) }}
                            </TableCell>
                            
                            <!-- Created -->
                            <TableCell v-if="!hiddenColumns.has('created')">
                                <div class="flex items-center gap-1 text-sm text-muted-foreground">
                                    <Calendar class="h-3 w-3" />
                                    <span>{{ formatDate(item.created) }}</span>
                                </div>
                            </TableCell>
                            
                            <!-- Incident Name -->
                            <TableCell v-if="!hiddenColumns.has('name_incident')" class="text-sm">
                                {{ item.name_incident || '—' }}
                            </TableCell>
                            
                            <!-- Actions -->
                            <TableCell v-if="!hiddenColumns.has('actions')">
                                <div class="flex items-center gap-1">
                                    <Button 
                                        v-if="item.url_post"
                                        variant="ghost" 
                                        size="sm" 
                                        @click="openUrl(item.url_post)"
                                        class="h-8 w-8 p-0"
                                    >
                                        <ExternalLink class="h-4 w-4" />
                                    </Button>
                                    <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        class="h-8 w-8 p-0"
                                    >
                                        <Eye class="h-4 w-4" />
                                    </Button>
                                </div>
                            </TableCell>
                        </TableRow>
                        
                        <!-- Loading Row -->
                        <TableRow v-if="isLoading">
                            <TableCell :colspan="visibleColumns.length" class="text-center py-8">
                                <RefreshCw class="h-6 w-6 animate-spin mx-auto mb-2" />
                                <p class="text-sm text-muted-foreground">Loading data...</p>
                            </TableCell>
                        </TableRow>
                        
                        <!-- Empty State -->
                        <TableRow v-if="!isLoading && incidentData.length === 0">
                            <TableCell :colspan="visibleColumns.length" class="text-center py-8 text-muted-foreground">
                                No incident data found.
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Pagination Controls -->
            <PaginationControls
                :current-page="currentPage"
                :total-pages="totalPages"
                :total-items="totalItems"
                :selected-items-per-page="selectedItemsPerPage"
                :has-next-page="hasNextPage"
                :has-prev-page="hasPrevPage"
                @go-to-page="goToPage"
                @next-page="nextPage"
                @prev-page="prevPage"
                @go-to-first-page="goToFirstPage"
                @go-to-last-page="goToLastPage"
            />

            <!-- Performance Info -->
            <div class="text-xs text-muted-foreground text-center pt-2">
                🚀 Optimized for large datasets - 
                <span v-if="selectedItemsPerPage === 'all'">
                    showing all {{ formatNumber(totalItems) }} items
                </span>
                <span v-else>
                    showing {{ selectedItemsPerPage }} items per page for better performance
                </span>
            </div>
        </CardContent>
    </Card>
</template>
