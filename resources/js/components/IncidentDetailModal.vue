<script setup lang="ts">
import { computed } from 'vue';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
    X, 
    ExternalLink, 
    Calendar, 
    Building, 
    Globe, 
    Hash, 
    Heart, 
    Share, 
    MessageSquare, 
    BarChart3,
    FileText,
    Link
} from 'lucide-vue-next';
import { formatDate, formatNumber } from '@/utils/formatters';

interface Props {
    isOpen: boolean;
    item: any;
    itemIndex: number;
    channelName: string;
}

interface Emits {
    (e: 'close'): void;
    (e: 'openUrl', url: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleClose = () => {
    emit('close');
};

const handleOpenUrl = (url: string) => {
    if (url) {
        emit('openUrl', url);
    }
};

// Format the content for display (preserve HTML but make it readable)
const formattedContent = computed(() => {
    if (!props.item?.content) return '';
    
    // Replace <br> tags with actual line breaks for better readability
    return props.item.content
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p>/gi, '\n\n')
        .replace(/<p>/gi, '')
        .replace(/<[^>]*>/g, ''); // Remove other HTML tags for display
});

const formattedTitle = computed(() => {
    if (!props.item?.title) return '';
    
    // Clean up title HTML for display
    return props.item.title
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<[^>]*>/g, ''); // Remove HTML tags for display
});

// Check if URL is valid
const isValidUrl = (url: string) => {
    if (!url) return false;
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};
</script>

<template>
    <!-- Modal Overlay -->
    <div 
        v-if="isOpen" 
        class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        @click="handleClose"
    >
        <!-- Modal Content -->
        <Card 
            class="w-full max-w-4xl max-h-[90vh] overflow-hidden"
            @click.stop
        >
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
                <div class="flex-1 min-w-0">
                    <CardTitle class="flex items-center gap-2 text-lg">
                        <FileText class="h-5 w-5" />
                        Chi tiết sự kiện #{{ itemIndex + 1 }}
                    </CardTitle>
                    <CardDescription class="flex items-center gap-2 mt-1">
                        <Building class="h-4 w-4" />
                        {{ channelName }}
                        <span v-if="item?.created" class="flex items-center gap-1 ml-4">
                            <Calendar class="h-4 w-4" />
                            {{ formatDate(item.created) }}
                        </span>
                    </CardDescription>
                </div>
                <Button 
                    variant="ghost" 
                    size="sm" 
                    @click="handleClose"
                >
                    <X class="h-4 w-4" />
                </Button>
            </CardHeader>
            
            <CardContent class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div class="space-y-6">
                    <!-- Title Section -->
                    <div v-if="formattedTitle" class="space-y-2">
                        <div class="flex items-center gap-2">
                            <Hash class="h-4 w-4 text-muted-foreground" />
                            <h3 class="text-sm font-medium text-muted-foreground">Tiêu đề</h3>
                        </div>
                        <div class="p-4 bg-muted/50 rounded-lg">
                            <p class="text-sm leading-relaxed whitespace-pre-wrap">{{ formattedTitle }}</p>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div v-if="formattedContent" class="space-y-2">
                        <div class="flex items-center gap-2">
                            <FileText class="h-4 w-4 text-muted-foreground" />
                            <h3 class="text-sm font-medium text-muted-foreground">Nội dung</h3>
                        </div>
                        <div class="p-4 bg-muted/50 rounded-lg">
                            <p class="text-sm leading-relaxed whitespace-pre-wrap">{{ formattedContent }}</p>
                        </div>
                    </div>

                    <!-- URL Section -->
                    <div v-if="item?.url_post" class="space-y-2">
                        <div class="flex items-center gap-2">
                            <Link class="h-4 w-4 text-muted-foreground" />
                            <h3 class="text-sm font-medium text-muted-foreground">Liên kết bài viết</h3>
                        </div>
                        <div class="p-4 bg-muted/50 rounded-lg">
                            <div class="flex items-center gap-2">
                                <Input
                                    :model-value="item.url_post"
                                    readonly
                                    class="flex-1 text-sm"
                                />
                                <Button
                                    v-if="isValidUrl(item.url_post)"
                                    variant="outline"
                                    size="sm"
                                    @click="handleOpenUrl(item.url_post)"
                                    class="flex items-center gap-2"
                                >
                                    <ExternalLink class="h-4 w-4" />
                                    Mở liên kết
                                </Button>
                            </div>
                        </div>
                    </div>

                    <!-- Metadata Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Company -->
                        <div v-if="item?.company" class="space-y-2">
                            <div class="flex items-center gap-2">
                                <Building class="h-4 w-4 text-muted-foreground" />
                                <h3 class="text-sm font-medium text-muted-foreground">Công ty</h3>
                            </div>
                            <Badge variant="outline" class="text-sm">
                                {{ item.company }}
                            </Badge>
                        </div>

                        <!-- Platform -->
                        <div v-if="item?.platform" class="space-y-2">
                            <div class="flex items-center gap-2">
                                <Globe class="h-4 w-4 text-muted-foreground" />
                                <h3 class="text-sm font-medium text-muted-foreground">Nền tảng</h3>
                            </div>
                            <Badge variant="secondary" class="text-sm">
                                {{ item.platform }}
                            </Badge>
                        </div>

                        <!-- Source -->
                        <div v-if="item?.source" class="space-y-2">
                            <div class="flex items-center gap-2">
                                <Globe class="h-4 w-4 text-muted-foreground" />
                                <h3 class="text-sm font-medium text-muted-foreground">Nguồn</h3>
                            </div>
                            <Badge variant="outline" class="text-sm">
                                {{ item.source }}
                            </Badge>
                        </div>

                        <!-- Incident Name -->
                        <div v-if="item?.name_incident" class="space-y-2">
                            <div class="flex items-center gap-2">
                                <FileText class="h-4 w-4 text-muted-foreground" />
                                <h3 class="text-sm font-medium text-muted-foreground">Tên sự kiện</h3>
                            </div>
                            <Badge variant="outline" class="text-sm">
                                {{ item.name_incident }}
                            </Badge>
                        </div>
                    </div>

                    <!-- Statistics Section -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-2">
                            <BarChart3 class="h-4 w-4 text-muted-foreground" />
                            <h3 class="text-sm font-medium text-muted-foreground">Thống kê tương tác</h3>
                        </div>
                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <!-- Daily Likes -->
                            <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                <div class="flex items-center gap-2 mb-1">
                                    <Heart class="h-4 w-4 text-red-500" />
                                    <span class="text-xs font-medium text-red-700">Lượt thích</span>
                                </div>
                                <div class="text-lg font-bold text-red-600">
                                    {{ formatNumber(item?.daily_like || 0) }}
                                </div>
                            </div>

                            <!-- Daily Shares -->
                            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center gap-2 mb-1">
                                    <Share class="h-4 w-4 text-blue-500" />
                                    <span class="text-xs font-medium text-blue-700">Chia sẻ</span>
                                </div>
                                <div class="text-lg font-bold text-blue-600">
                                    {{ formatNumber(item?.daily_share || 0) }}
                                </div>
                            </div>

                            <!-- Daily Comments -->
                            <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center gap-2 mb-1">
                                    <MessageSquare class="h-4 w-4 text-green-500" />
                                    <span class="text-xs font-medium text-green-700">Bình luận</span>
                                </div>
                                <div class="text-lg font-bold text-green-600">
                                    {{ formatNumber(item?.daily_comment || 0) }}
                                </div>
                            </div>

                            <!-- Total -->
                            <div class="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                                <div class="flex items-center gap-2 mb-1">
                                    <BarChart3 class="h-4 w-4 text-purple-500" />
                                    <span class="text-xs font-medium text-purple-700">Tổng cộng</span>
                                </div>
                                <div class="text-lg font-bold text-purple-600">
                                    {{ formatNumber(item?.total || 0) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Raw Data Section (for debugging/technical users) -->
                    <details class="space-y-2">
                        <summary class="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                            Dữ liệu thô (Raw Data)
                        </summary>
                        <div class="p-4 bg-muted/30 rounded-lg">
                            <pre class="text-xs text-muted-foreground overflow-x-auto">{{ JSON.stringify(item, null, 2) }}</pre>
                        </div>
                    </details>
                </div>
            </CardContent>
        </Card>
    </div>
</template>
