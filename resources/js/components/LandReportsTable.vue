<script setup lang="ts">
import { type LandReport } from '@/types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Eye, Calendar } from 'lucide-vue-next';

interface Props {
    reports: LandReport[];
    showActions?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showActions: true
});

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
};

const formatArea = (area: number) => {
    return `${area.toFixed(1)} ha`;
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

const getStatusVariant = (status: LandReport['status']) => {
    switch (status) {
        case 'available':
            return 'default';
        case 'sold':
            return 'secondary';
        case 'pending':
            return 'outline';
        case 'reserved':
            return 'destructive';
        default:
            return 'outline';
    }
};

const getLandTypeColor = (landType: LandReport['landType']) => {
    switch (landType) {
        case 'agricultural':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'residential':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'commercial':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'industrial':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        case 'forest':
            return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
};
</script>

<template>
    <div class="rounded-md border">
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Property</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Area</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead class="hidden lg:table-cell">Last Updated</TableHead>
                    <TableHead v-if="showActions" class="w-[100px]">Actions</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <TableRow v-for="report in reports" :key="report.id" class="hover:bg-muted/50">
                    <TableCell>
                        <div class="space-y-1">
                            <div class="font-medium">{{ report.title }}</div>
                            <div v-if="report.description" class="text-sm text-muted-foreground line-clamp-1">
                                {{ report.description }}
                            </div>
                        </div>
                    </TableCell>
                    <TableCell>
                        <div class="flex items-center gap-1">
                            <MapPin class="h-3 w-3 text-muted-foreground" />
                            <span class="text-sm">{{ report.location }}</span>
                        </div>
                    </TableCell>
                    <TableCell>
                        <Badge :class="getLandTypeColor(report.landType)" class="text-xs capitalize">
                            {{ report.landType }}
                        </Badge>
                    </TableCell>
                    <TableCell class="text-sm">
                        {{ formatArea(report.area) }}
                    </TableCell>
                    <TableCell class="font-medium">
                        {{ formatCurrency(report.value) }}
                    </TableCell>
                    <TableCell>
                        <Badge :variant="getStatusVariant(report.status)" class="text-xs capitalize">
                            {{ report.status }}
                        </Badge>
                    </TableCell>
                    <TableCell class="hidden lg:table-cell">
                        <div class="flex items-center gap-1 text-sm text-muted-foreground">
                            <Calendar class="h-3 w-3" />
                            <span>{{ formatDate(report.lastUpdated) }}</span>
                        </div>
                    </TableCell>
                    <TableCell v-if="showActions">
                        <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                            <Eye class="h-4 w-4" />
                        </Button>
                    </TableCell>
                </TableRow>
                <TableRow v-if="reports.length === 0">
                    <TableCell :colspan="showActions ? 8 : 7" class="text-center py-8 text-muted-foreground">
                        No land reports found.
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </div>
</template>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
