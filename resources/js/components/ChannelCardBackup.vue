<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { type Channel, type IncidentData } from '@/types';
import {
    ArrowDown,
    ArrowUp,
    ArrowUpDown,
    Building,
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    ExternalLink,
    RefreshCw,
    Search,
    Settings2,
    X,
} from 'lucide-vue-next';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
    channel: Channel;
}

const props = defineProps<Props>();

// Table state
const allIncidentData = ref<IncidentData[]>([]);
const isLoading = ref(false);
const error = ref('');
const searchQuery = ref('');
const sortKey = ref<string>('created');
const sortDirection = ref<'asc' | 'desc'>('desc');

// Pagination for performance with large datasets
const itemsPerPageOptions = [10, 20, 50, 100, 200, 'all'] as const;
const selectedItemsPerPage = ref<number | 'all'>(10);
const currentPage = ref(1);
const isLoadingMore = ref(false);

// Computed items per page
const ITEMS_PER_PAGE = computed(() => {
    return selectedItemsPerPage.value === 'all' ? totalItems.value : selectedItemsPerPage.value;
});

// Column visibility
const hiddenColumns = ref<Set<string>>(new Set());

// Content editing state
const editingContent = ref<number | null>(null);
const editingValue = ref<string>('');

// Define table columns
const columns = [
    { key: 'id', label: 'ID', sortable: true, hideable: true, width: '80px' },
    { key: 'title', label: 'Title', sortable: true, hideable: true, width: '300px' },
    { key: 'content', label: 'Content', sortable: true, hideable: false, width: '300px' },
    { key: 'company', label: 'Company', sortable: true, hideable: true, width: '150px' },
    { key: 'platform', label: 'Platform', sortable: true, hideable: true, width: '120px' },
    { key: 'source', label: 'Source', sortable: true, hideable: true, width: '120px' },
    { key: 'daily_like', label: 'Daily Likes', sortable: true, hideable: true, width: '100px' },
    { key: 'daily_share', label: 'Daily Shares', sortable: true, hideable: true, width: '100px' },
    { key: 'daily_comment', label: 'Daily Comments', sortable: true, hideable: true, width: '120px' },
    { key: 'total', label: 'Total', sortable: true, hideable: true, width: '100px' },
    { key: 'created', label: 'Created', sortable: true, hideable: true, width: '150px' },
    { key: 'name_incident', label: 'Incident Name', sortable: true, hideable: true, width: '200px' },
];

const visibleColumns = computed(() => columns.filter((col) => !hiddenColumns.value.has(col.key)));

// Filtered and sorted data
const filteredAndSortedData = computed(() => {
    let result = [...allIncidentData.value];

    // Apply search filter
    if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase().trim();
        result = result.filter((item) => {
            return (
                item.title?.toLowerCase().includes(query) ||
                item.content?.toLowerCase().includes(query) ||
                item.company?.toLowerCase().includes(query) ||
                item.platform?.toLowerCase().includes(query) ||
                item.source?.toLowerCase().includes(query) ||
                item.name_incident?.toLowerCase().includes(query)
            );
        });
    }

    // Apply sorting
    if (sortKey.value) {
        result.sort((a, b) => {
            const aValue = getNestedValue(a, sortKey.value);
            const bValue = getNestedValue(b, sortKey.value);

            // Handle different data types
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return sortDirection.value === 'asc' ? aValue - bValue : bValue - aValue;
            }

            if (aValue instanceof Date && bValue instanceof Date) {
                return sortDirection.value === 'asc' ? aValue.getTime() - bValue.getTime() : bValue.getTime() - aValue.getTime();
            }

            // String comparison
            const aStr = String(aValue || '').toLowerCase();
            const bStr = String(bValue || '').toLowerCase();

            if (sortDirection.value === 'asc') {
                return aStr.localeCompare(bStr);
            } else {
                return bStr.localeCompare(aStr);
            }
        });
    }

    return result;
});

// Paginated display - show selected items per page for performance
const incidentData = computed(() => {
    if (selectedItemsPerPage.value === 'all') {
        return filteredAndSortedData.value;
    }
    const startIndex = (currentPage.value - 1) * ITEMS_PER_PAGE.value;
    const endIndex = startIndex + ITEMS_PER_PAGE.value;
    return filteredAndSortedData.value.slice(startIndex, endIndex);
});

// Pagination computed properties
const totalItems = computed(() => filteredAndSortedData.value.length);
const totalPages = computed(() => {
    if (selectedItemsPerPage.value === 'all') return 1;
    return Math.ceil(totalItems.value / ITEMS_PER_PAGE.value);
});
const hasNextPage = computed(() => currentPage.value < totalPages.value);
const hasPrevPage = computed(() => currentPage.value > 1);
const startItem = computed(() => {
    if (selectedItemsPerPage.value === 'all') return 1;
    return (currentPage.value - 1) * ITEMS_PER_PAGE.value + 1;
});
const endItem = computed(() => {
    if (selectedItemsPerPage.value === 'all') return totalItems.value;
    return Math.min(currentPage.value * ITEMS_PER_PAGE.value, totalItems.value);
});

// Utility function to get nested object values
const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Fetch data function - only fetches data once
const fetchData = async (page = 1, append = false) => {
    if (!props.channel.id) return;

    isLoading.value = true;
    error.value = '';
    const token = route().params.token ?? '';

    try {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: '1000',
            token: token,// Fetch more data at once
        });

        const response = await fetch(`/api/incident-data/channel/${props.channel.id}?${params}`);

        if (!response.ok) {
            throw new Error('Failed to fetch data');
        }

        const result = await response.json();

        if (result.success) {
            if (append) {
                allIncidentData.value = [...allIncidentData.value, ...result.data];
            } else {
                allIncidentData.value = result.data;
            }

            // If there's more data, fetch it
            if (result.pagination.hasMore) {
                await fetchData(page + 1, true);
            }
        } else {
            error.value = result.error || 'Failed to fetch data';
        }
    } catch (err) {
        error.value = 'Network error occurred';
        console.error('Error fetching incident data:', err);
    } finally {
        isLoading.value = false;
    }
};

// Refresh data
const refreshData = () => {
    currentPage.value = 1;
    fetchData(1, false);
};

// Search functionality
const clearSearch = () => {
    searchQuery.value = '';
    currentPage.value = 1; // Reset to first page when searching
};

// Sorting functionality
const toggleSort = (columnKey: string) => {
    if (sortKey.value === columnKey) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        sortKey.value = columnKey;
        sortDirection.value = 'asc';
    }
    currentPage.value = 1; // Reset to first page when sorting
};

// Watch for search changes to reset pagination
watch(searchQuery, () => {
    currentPage.value = 1;
});

const getSortIcon = (columnKey: string) => {
    if (sortKey.value !== columnKey) return ArrowUpDown;
    return sortDirection.value === 'asc' ? ArrowUp : ArrowDown;
};

// Column visibility controls
const toggleColumn = (columnKey: string) => {
    if (hiddenColumns.value.has(columnKey)) {
        hiddenColumns.value.delete(columnKey);
    } else {
        hiddenColumns.value.add(columnKey);
    }
};

const showAllColumns = () => {
    hiddenColumns.value.clear();
};

const hideAllColumns = () => {
    columns.forEach((col) => {
        if (col.hideable) {
            hiddenColumns.value.add(col.key);
        }
    });
};

// Pagination navigation functions
const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
    }
};

const nextPage = () => {
    if (hasNextPage.value) {
        currentPage.value += 1;
    }
};

const prevPage = () => {
    if (hasPrevPage.value) {
        currentPage.value -= 1;
    }
};

const goToFirstPage = () => {
    currentPage.value = 1;
};

const goToLastPage = () => {
    currentPage.value = totalPages.value;
};

// Handle items per page change
const changeItemsPerPage = (value: string) => {
    selectedItemsPerPage.value = value === 'all' ? 'all' : parseInt(value);
    currentPage.value = 1; // Reset to first page
};

// Content editing functions
const startEditingContent = async (itemId: number, content: string) => {
    editingContent.value = itemId;
    editingValue.value = content || '';

    // Wait for DOM update and focus the textarea
    await nextTick();
    const textarea = document.querySelector('.content-editor textarea') as HTMLTextAreaElement;
    if (textarea) {
        textarea.focus();
        textarea.select(); // Select all text for easy editing
    }
};

const stopEditingContent = () => {
    editingContent.value = null;
    editingValue.value = '';
};

const isEditingContent = (itemId: number) => {
    return editingContent.value === itemId;
};

const getDisplayContent = (content: string) => {
    if (!content) return '';

    // Show only first line (up to first line break or 100 characters)
    const firstLine = content.split('\n')[0];
    if (firstLine.length <= 100) {
        return firstLine;
    }

    return firstLine.substring(0, 100) + '...';
};

const handleContentKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        stopEditingContent();
    } else if (event.key === 'Enter' && !event.shiftKey) {
        // Save content (in real app, you'd save to backend)
        stopEditingContent();
    }
};

// Click outside to close editing
const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.content-editor')) {
        stopEditingContent();
    }
};

// Watch for search changes to reset to first page
watch(searchQuery, () => {
    currentPage.value = 1;
});

// Format helpers
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
};

const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank');
    }
};

// Initialize data on mount
onMounted(() => {
    fetchData();
    // Add click outside listener
    document.addEventListener('click', handleClickOutside);
});

// Cleanup on unmount
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
    <Card class="w-full">
        <CardHeader>
            <div class="flex items-center justify-between">
                <div>
                    <CardTitle class="flex items-center gap-2">
                        <Building class="h-5 w-5" />
                        {{ channel.name }}
                    </CardTitle>
                </div>
                <Badge variant="outline"> {{ formatNumber(totalItems) }} bản ghi </Badge>
            </div>
        </CardHeader>

        <CardContent class="space-y-4">
            <!-- Controls -->
            <div class="flex items-center justify-between gap-4">
                <!-- Search -->
                <div class="relative max-w-sm flex-1">
                    <Search class="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input v-model="searchQuery" placeholder="Tìm kiếm ..." class="pr-9 pl-9" />
                    <Button
                        v-if="searchQuery"
                        variant="ghost"
                        size="sm"
                        class="absolute top-1/2 right-1 h-7 w-7 -translate-y-1/2 p-0"
                        @click="clearSearch"
                    >
                        <X class="h-3 w-3" />
                    </Button>
                </div>

                <div class="flex items-center gap-2">
                    <!-- Items Per Page Selector -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm whitespace-nowrap text-muted-foreground">Hiển thị:</span>
                        <Select :model-value="selectedItemsPerPage.toString()" @update:model-value="changeItemsPerPage" class="w-20">
                            <option v-for="option in itemsPerPageOptions" :key="option" :value="option.toString()">
                                {{ option === 'all' ? 'All' : option }}
                            </option>
                        </Select>
                    </div>

                    <!-- Refresh Button -->
                    <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
                        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': isLoading }" />
                        Load lại data
                    </Button>

                    <!-- Column Toggle -->
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="outline" size="sm">
                                <Settings2 class="mr-2 h-4 w-4" />
                                Cột
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" class="w-48">
                            <DropdownMenuLabel>Chuyển đổi cột</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuCheckboxItem
                                v-for="column in columns.filter((col) => col.hideable)"
                                :key="column.key"
                                :checked="!hiddenColumns.has(column.key)"
                                @click="toggleColumn(column.key)"
                            >
                                {{ column.label }}
                            </DropdownMenuCheckboxItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuCheckboxItem @click="showAllColumns"> Hiển thị tất </DropdownMenuCheckboxItem>
                            <DropdownMenuCheckboxItem @click="hideAllColumns"> Ẩn tất </DropdownMenuCheckboxItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            <!-- Results Info -->
            <div class="flex items-center justify-between text-sm text-muted-foreground">
                <div>
                    Hiển thị {{ startItem }} - {{ endItem }} trong tổng số {{ formatNumber(totalItems) }} results
                    <span v-if="searchQuery"> for "{{ searchQuery }}"</span>
                    <span v-if="totalPages > 1" class="ml-2"> • Trang {{ currentPage }} trong tổng số {{ totalPages }} </span>
                </div>
                <div v-if="sortKey" class="flex items-center gap-1">
                    <span>Sắp xếp theo {{ columns.find((col) => col.key === sortKey)?.label }}</span>
                    <component :is="getSortIcon(sortKey)" class="h-3 w-3" />
                </div>
            </div>

            <!-- Error Message -->
            <div v-if="error" class="rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-600">
                {{ error }}
            </div>

            <!-- Table -->
            <div class="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead
                                v-for="column in visibleColumns"
                                :key="column.key"
                                :class="[
                                    column.sortable ? 'cursor-pointer select-none hover:bg-muted/50' : '',
                                    column.width ? `w-[${column.width}]` : '',
                                ]"
                                @click="column.sortable ? toggleSort(column.key) : null"
                            >
                                <div class="flex items-center gap-2">
                                    <span>{{ column.label }}</span>
                                    <component
                                        v-if="column.sortable"
                                        :is="getSortIcon(column.key)"
                                        class="h-4 w-4"
                                        :class="sortKey === column.key ? 'text-foreground' : 'text-muted-foreground'"
                                    />
                                </div>
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in incidentData" :key="item.id" class="hover:bg-muted/50">
                            <!-- ID -->
                            <TableCell v-if="!hiddenColumns.has('id')" class="font-mono text-xs">
                                {{ item.id }}
                            </TableCell>

                            <!-- Title -->
                            <TableCell v-if="!hiddenColumns.has('title')" class="max-w-xs">
                                <div class="space-y-2">
                                    <div class="font-medium break-words whitespace-normal">
                                        {{ item.title }}
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell v-if="!hiddenColumns.has('content')" class="max-w-xs">
                                <div class="space-y-2">
                                    <div v-if="item.content" class="text-sm text-muted-foreground">
                                        <!-- Editing Mode -->
                                        <div v-if="isEditingContent(item.id)" class="content-editor p-2 border-2 border-blue-200 rounded-md bg-blue-50/50">
                                            <textarea
                                                v-model="editingValue"
                                                class="w-full min-h-[120px] p-3 text-sm border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                @keydown="handleContentKeydown"
                                                @click.stop
                                                placeholder="Enter content..."
                                                ref="contentTextarea"
                                            ></textarea>
                                            <div class="flex items-center justify-between mt-2 text-xs">
                                                <span class="text-muted-foreground">Press Enter to save, Esc to cancel</span>
                                                <div class="flex gap-2">
                                                    <button
                                                        @click="stopEditingContent"
                                                        class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        @click="stopEditingContent"
                                                        class="px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded"
                                                    >
                                                        Save
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Display Mode -->
                                        <div
                                            v-else
                                            class="break-words whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer hover:bg-muted/50 p-2 rounded transition-colors border border-transparent hover:border-gray-200"
                                            @click="startEditingContent(item.id, item.content)"
                                            :title="item.content"
                                        >
                                            {{ getDisplayContent(item.content) || 'Click to edit content...' }}
                                        </div>
                                    </div>
                                </div>
                            </TableCell>
                            <!-- Company -->
                            <TableCell v-if="!hiddenColumns.has('company')" class="text-sm">
                                {{ item.company || '—' }}
                            </TableCell>

                            <!-- Platform -->
                            <TableCell v-if="!hiddenColumns.has('platform')">
                                <Badge variant="outline" class="text-xs">
                                    {{ item.platform || '—' }}
                                </Badge>
                            </TableCell>

                            <!-- Source -->
                            <TableCell v-if="!hiddenColumns.has('source')" class="text-sm">
                                {{ item.source || '—' }}
                            </TableCell>

                            <!-- Daily Likes -->
                            <TableCell v-if="!hiddenColumns.has('daily_like')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <span class="text-sm">{{ formatNumber(item.daily_like) }}</span>
                                </div>
                            </TableCell>

                            <!-- Daily Shares -->
                            <TableCell v-if="!hiddenColumns.has('daily_share')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <span class="text-sm">{{ formatNumber(item.daily_share) }}</span>
                                </div>
                            </TableCell>

                            <!-- Daily Comments -->
                            <TableCell v-if="!hiddenColumns.has('daily_comment')" class="text-right">
                                <div class="flex items-center justify-end gap-1">
                                    <span class="text-sm">{{ formatNumber(item.daily_comment) }}</span>
                                </div>
                            </TableCell>

                            <!-- Total -->
                            <TableCell v-if="!hiddenColumns.has('total')" class="text-right font-medium">
                                {{ formatNumber(item.total) }}
                            </TableCell>

                            <!-- Created -->
                            <TableCell v-if="!hiddenColumns.has('created')">
                                <div class="flex items-center gap-1 text-sm text-muted-foreground">
                                    <span>{{ formatDate(item.created) }}</span>
                                </div>
                            </TableCell>

                            <!-- Incident Name -->
                            <TableCell v-if="!hiddenColumns.has('name_incident')" class="text-sm">
                                {{ item.name_incident || '—' }}
                            </TableCell>

                            <!-- Actions -->
                            <TableCell v-if="!hiddenColumns.has('actions')">
                                <div class="flex items-center gap-1">
                                    <Button v-if="item.url_post" variant="ghost" size="sm" @click="openUrl(item.url_post)" class="h-8 w-8 p-0">
                                        <ExternalLink class="h-4 w-4" />
                                    </Button>

                                </div>
                            </TableCell>
                        </TableRow>

                        <!-- Loading Row -->
                        <TableRow v-if="isLoading">
                            <TableCell :colspan="visibleColumns.length" class="py-8 text-center">
                                <RefreshCw class="mx-auto mb-2 h-6 w-6 animate-spin" />
                                <p class="text-sm text-muted-foreground">Loading data...</p>
                            </TableCell>
                        </TableRow>

                        <!-- Empty State -->
                        <TableRow v-if="!isLoading && incidentData.length === 0">
                            <TableCell :colspan="visibleColumns.length" class="py-8 text-center text-muted-foreground">
                                No incident data found.
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Pagination Controls -->
            <div v-if="totalPages > 1 && selectedItemsPerPage !== 'all'" class="flex items-center justify-between pt-4">
                <div class="text-sm text-muted-foreground">
                    Page {{ currentPage }} of {{ totalPages }} ({{ formatNumber(totalItems) }} total items)
                </div>

                <div class="flex items-center gap-2">
                    <!-- First Page -->
                    <Button variant="outline" size="sm" @click="goToFirstPage" :disabled="!hasPrevPage">
                        <ChevronsLeft class="h-4 w-4" />
                    </Button>

                    <!-- Previous Page -->
                    <Button variant="outline" size="sm" @click="prevPage" :disabled="!hasPrevPage">
                        <ChevronLeft class="h-4 w-4" />
                        Previous
                    </Button>

                    <!-- Page Numbers -->
                    <div class="flex items-center gap-1">
                        <template v-for="page in Math.min(5, totalPages)" :key="page">
                            <Button
                                v-if="page <= 3 || page > totalPages - 2 || Math.abs(page - currentPage) <= 1"
                                :variant="page === currentPage ? 'default' : 'outline'"
                                size="sm"
                                @click="goToPage(page)"
                                class="w-10"
                            >
                                {{ page }}
                            </Button>
                        </template>
                        <span v-if="totalPages > 5 && currentPage < totalPages - 2" class="px-2">...</span>
                    </div>

                    <!-- Next Page -->
                    <Button variant="outline" size="sm" @click="nextPage" :disabled="!hasNextPage">
                        Next
                        <ChevronRight class="h-4 w-4" />
                    </Button>

                    <!-- Last Page -->
                    <Button variant="outline" size="sm" @click="goToLastPage" :disabled="!hasNextPage">
                        <ChevronsRight class="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </CardContent>
    </Card>
</template>

<style scoped>
/* Text wrapping styles for better readability */
.whitespace-normal {
    white-space: normal;
}

.break-words {
    word-break: break-words;
    overflow-wrap: break-word;
}
</style>
