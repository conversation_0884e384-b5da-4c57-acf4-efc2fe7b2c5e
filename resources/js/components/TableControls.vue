<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Select } from '@/components/ui/select';
import { RefreshCw, Search, Settings2, X, FileSpreadsheet } from 'lucide-vue-next';

interface Column {
    key: string;
    label: string;
    hideable: boolean;
}

interface Props {
    searchQuery: string;
    selectedItemsPerPage: number | 'all';
    itemsPerPageOptions: readonly (number | 'all')[];
    hiddenColumns: Set<string>;
    columns: Column[];
    isLoading: boolean;
}

interface Emits {
    (e: 'update:searchQuery', value: string): void;
    (e: 'update:selectedItemsPerPage', value: number | 'all'): void;
    (e: 'clearSearch'): void;
    (e: 'refresh'): void;
    (e: 'toggleColumn', columnKey: string): void;
    (e: 'showAllColumns'): void;
    (e: 'hideAllColumns'): void;
    (e: 'resetColumnWidths'): void;
    (e: 'resetColumnOrder'): void;
    (e: 'openExport'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleSearchUpdate = (value: string) => {
    emit('update:searchQuery', value);
};

const handleItemsPerPageChange = (value: string) => {
    const parsedValue = value === 'all' ? 'all' : parseInt(value);
    emit('update:selectedItemsPerPage', parsedValue);
};

const clearSearch = () => {
    emit('clearSearch');
};

const refresh = () => {
    emit('refresh');
};

const toggleColumn = (columnKey: string) => {
    emit('toggleColumn', columnKey);
};

const showAllColumns = () => {
    emit('showAllColumns');
};

const hideAllColumns = () => {
    emit('hideAllColumns');
};

const resetColumnWidths = () => {
    emit('resetColumnWidths');
};

const resetColumnOrder = () => {
    emit('resetColumnOrder');
};

const openExport = () => {
    emit('openExport');
};
</script>

<template>
    <div class="flex items-center justify-between gap-4">
        <!-- Search -->
        <div class="relative flex-1 max-w-sm">
            <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
                :model-value="searchQuery"
                @update:model-value="handleSearchUpdate"
                placeholder="Tìm kiếm sự kiện..."
                class="pl-9 pr-9"
            />
            <Button
                v-if="searchQuery"
                variant="ghost"
                size="sm"
                class="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0"
                @click="clearSearch"
            >
                <X class="h-3 w-3" />
            </Button>
        </div>

        <div class="flex items-center gap-2">
            <!-- Items Per Page Selector -->
            <div class="flex items-center gap-2">
                <span class="text-sm text-muted-foreground whitespace-nowrap">Hiển thị:</span>
                <Select
                    :model-value="selectedItemsPerPage.toString()"
                    @update:model-value="handleItemsPerPageChange"
                    class="w-20"
                >
                    <option
                        v-for="option in itemsPerPageOptions"
                        :key="option"
                        :value="option.toString()"
                    >
                        {{ option === 'all' ? 'Tất cả' : option }}
                    </option>
                </Select>
            </div>

            <!-- Excel Export Button -->
            <Button
                variant="outline"
                size="sm"
                @click="openExport"
                :disabled="isLoading"
            >
                <FileSpreadsheet class="mr-2 h-4 w-4" />
                Xuất Excel
            </Button>

            <!-- Refresh Button -->
            <Button
                variant="outline"
                size="sm"
                @click="refresh"
                :disabled="isLoading"
            >
                <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': isLoading }" />
                Làm mới
            </Button>

            <!-- Column Toggle -->
            <DropdownMenu>
                <DropdownMenuTrigger as-child>
                    <Button variant="outline" size="sm">
                        <Settings2 class="mr-2 h-4 w-4" />
                        Cột
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" class="w-48">
                    <DropdownMenuLabel>Tùy chỉnh cột</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem
                        v-for="column in columns.filter(col => col.hideable)"
                        :key="column.key"
                        :checked="!hiddenColumns.has(column.key)"
                        @click="toggleColumn(column.key)"
                    >
                        {{ column.label }}
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem @click="showAllColumns">
                        Hiển thị tất cả
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem @click="hideAllColumns">
                        Ẩn tất cả
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem @click="resetColumnWidths">
                        Đặt lại độ rộng cột
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem @click="resetColumnOrder">
                        Đặt lại thứ tự cột
                    </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </div>
</template>
