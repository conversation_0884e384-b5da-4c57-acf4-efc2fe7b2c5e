<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { type Channel } from '@/types';
import {
    ArrowDown,
    ArrowUp,
    ArrowUpDown,
    Building,
    Calendar,
    ExternalLink,
    Eye,
    GripVertical,
    Heart,
    MessageSquare,
    RefreshCw,
    Share,
} from 'lucide-vue-next';
// Components
import TableControls from '@/components/TableControls.vue';
import PaginationControls from '@/components/PaginationControls.vue';
import ResizableTableHeader from '@/components/ResizableTableHeader.vue';
import ExcelExportDialog from '@/components/ExcelExportDialog.vue';
import IncidentDetailModal from '@/components/IncidentDetailModal.vue';
// Composables and utilities
import { useIncidentDataTable } from '@/composables/useIncidentDataTable';
import { useColumnResize } from '@/composables/useColumnResize';
import { useColumnReorder } from '@/composables/useColumnReorder';
import { useExcelExport } from '@/composables/useExcelExport';
import { incidentDataColumns } from '@/config/tableColumns';
import { formatDate, formatNumber, openUrl } from '@/utils/formatters';
interface Props {
    channel: Channel;
}

const props = defineProps<Props>();

// Use the table composable
const {
    incidentData,
    filteredAndSortedData,
    isLoading,
    error,
    searchQuery,
    sortKey,
    sortDirection,
    selectedItemsPerPage,
    itemsPerPageOptions,
    currentPage,
    hiddenColumns,
    totalItems,
    totalPages,
    hasNextPage,
    hasPrevPage,
    startItem,
    endItem,
    fetchData,
    refreshData,
    clearSearch,
    toggleSort,
    changeItemsPerPage,
    goToPage,
    nextPage,
    prevPage,
    goToFirstPage,
    goToLastPage,
    toggleColumn,
    showAllColumns,
    hideAllColumns,
} = useIncidentDataTable();

// Column resizing
const resizableColumns = incidentDataColumns.map(col => ({
    key: col.key,
    width: parseInt(col.width?.replace('px', '') || '150'),
    minWidth: col.minWidth || 80,
    maxWidth: col.maxWidth // Undefined for unlimited width
}));

const {

    resizingColumn,
    startResize,
    getColumnWidth,
    resetColumnWidths,
} = useColumnResize(resizableColumns);

// Column reordering
const reorderableColumns = incidentDataColumns.map(col => ({
    key: col.key,
    label: col.label,
    order: col.order || 0
}));

const {
    getOrderedColumns,
    startDrag,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    resetColumnOrder,
} = useColumnReorder(reorderableColumns);

// Excel export
const {
    isExporting,
    showExportDialog,
    exportColumns,
    exportToExcel,
    toggleColumn: toggleExportColumn,
    selectAllColumns,
    deselectAllColumns,
    openExportDialog,
} = useExcelExport();

// Detail modal state
const showDetailModal = ref(false);
const selectedItem = ref<any>(null);
const selectedItemIndex = ref(0);

// Computed properties
const visibleColumns = computed(() => {
    let filteredColumns = incidentDataColumns.filter(col => !hiddenColumns.value.has(col.key));

    // Hide title column when channel ID is 1
    if (props.channel.id === '1' || props.channel.id === 1) {
        filteredColumns = filteredColumns.filter(col => col.key !== 'title');
    }

    // Apply column ordering
    return getOrderedColumns(filteredColumns);
});

const getSortIcon = (columnKey: string) => {
    if (sortKey.value !== columnKey) return ArrowUpDown;
    return sortDirection.value === 'asc' ? ArrowUp : ArrowDown;
};



// Helper function to get cell content based on column key
const getCellContent = (item: any, columnKey: string, index: number) => {
    switch (columnKey) {
        case 'id':
            return index + 1;
        case 'title':
            return item.title || '';
        case 'content':
            return item.content || '';
        case 'company':
            return item.company || '—';
        case 'platform':
            return item.platform || '—';
        case 'source':
            return item.source || '—';
        case 'daily_like':
            return item.daily_like;
        case 'daily_share':
            return item.daily_share;
        case 'daily_comment':
            return item.daily_comment;
        case 'total':
            return item.total;
        case 'created':
            return item.created;
        case 'name_incident':
            return item.name_incident || '—';
        default:
            return '';
    }
};

// Helper function to check if column should be visible for this channel
const isColumnVisible = (columnKey: string) => {
    if (hiddenColumns.value.has(columnKey)) return false;
    if (columnKey === 'title' && (props.channel.id === '1' || props.channel.id === 1)) return false;
    return true;
};

// Excel export handlers
const handleOpenExport = () => {
    openExportDialog(incidentDataColumns, hiddenColumns.value, props.channel.id);
};

const handleExport = async (format: 'csv' | 'excel') => {
    try {
        // Use all filtered and sorted data (same order as table)
        // This preserves the exact sort order and includes all HTML content
        await exportToExcel(filteredAndSortedData.value, props.channel.name, format);
    } catch (error) {
        console.error('Export failed:', error);
        // You could add a toast notification here
    }
};

// Detail modal handlers
const handleRowClick = (item: any, index: number) => {
    selectedItem.value = item;
    selectedItemIndex.value = index;
    showDetailModal.value = true;
};

const handleCloseModal = () => {
    showDetailModal.value = false;
    selectedItem.value = null;
    selectedItemIndex.value = 0;
};

const handleOpenUrl = (url: string) => {
    if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }
};

// Get platform color for mobile cards
const getPlatformColor = (platform?: string) => {
    switch (platform?.toLowerCase()) {
        case 'facebook': return 'bg-blue-100 text-blue-800';
        case 'twitter': return 'bg-sky-100 text-sky-800';
        case 'linkedin': return 'bg-blue-100 text-blue-800';
        case 'instagram': return 'bg-pink-100 text-pink-800';
        case 'youtube': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

// Lifecycle
onMounted(() => {
    fetchData(props.channel.id);
});
</script>

<template>
    <Card class="w-full">
        <CardHeader>
            <div class="flex items-center justify-between">
                <div>
                    <CardTitle class="flex items-center gap-2">
                        <Building class="h-5 w-5" />
                        {{ channel.name }}
                    </CardTitle>
                    <CardDescription>
                        Channel ID: {{ channel.id }}
                        <span v-if="channel.token" class="ml-2">
                            • Token: <code class="bg-muted px-1 rounded text-xs">{{ channel.token }}</code>
                        </span>
                    </CardDescription>
                </div>
                <Badge variant="outline">
                    {{ formatNumber(totalItems) }} mục
                </Badge>
            </div>
        </CardHeader>

        <CardContent class="space-y-4">
            <!-- Table Controls -->
            <TableControls
                :search-query="searchQuery"
                :selected-items-per-page="selectedItemsPerPage"
                :items-per-page-options="itemsPerPageOptions"
                :hidden-columns="hiddenColumns"
                :columns="incidentDataColumns"
                :is-loading="isLoading"
                @update:search-query="searchQuery = $event"
                @update:selected-items-per-page="changeItemsPerPage"
                @clear-search="clearSearch"
                @refresh="refreshData(channel.id)"
                @toggle-column="toggleColumn"
                @show-all-columns="showAllColumns"
                @hide-all-columns="hideAllColumns"
                @reset-column-widths="resetColumnWidths"
                @reset-column-order="resetColumnOrder"
                @open-export="handleOpenExport"
            />

            <!-- Results Info -->
            <div class="flex items-center justify-between text-sm text-muted-foreground">
                <div>
                    Hiển thị {{ startItem }} - {{ endItem }} / {{ formatNumber(totalItems) }} kết quả
                    <span v-if="searchQuery"> cho "{{ searchQuery }}"</span>
                    <span v-if="totalPages > 1" class="ml-2">
                        • Trang {{ currentPage }} / {{ totalPages }}
                    </span>
                </div>
                <div v-if="sortKey" class="flex items-center gap-1">
                    <span>Sắp xếp theo {{ incidentDataColumns.find(col => col.key === sortKey)?.label }}</span>
                    <component :is="getSortIcon(sortKey)" class="h-3 w-3" />
                </div>
            </div>

            <!-- Error Message -->
            <div v-if="error" class="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
                {{ error }}
            </div>

            <!-- Table -->
            <div class="rounded-md border overflow-hidden">
                <!-- Mobile: Card Layout -->
                <div class="block md:hidden">
                    <div class="space-y-3 p-4">
                        <div
                            v-for="(item, index) in incidentData"
                            :key="index"
                            class="bg-card border rounded-lg p-4 space-y-3 hover:bg-muted/50 cursor-pointer transition-colors"
                            @click="handleRowClick(item, index)"
                        >
                            <!-- Mobile Card Header -->
                            <div class="flex items-start justify-between">
                                <div class="flex items-center gap-2">
                                    <Badge variant="outline" class="text-xs">
                                        #{{ index + 1 }}
                                    </Badge>
                                    <Badge v-if="item.platform" variant="secondary" class="text-xs" :class="getPlatformColor(item.platform)">
                                        {{ item.platform }}
                                    </Badge>
                                </div>
                                <div class="flex items-center gap-1" @click.stop>
                                    <Button
                                        v-if="item.url_post"
                                        variant="ghost"
                                        size="sm"
                                        @click="handleOpenUrl(item.url_post)"
                                        class="h-8 w-8 p-0"
                                    >
                                        <ExternalLink class="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>

                            <!-- Mobile Card Content -->
                            <div class="space-y-2">
                                <!-- Title -->
                                <div v-if="item.title && !(props.channel.id === '1' || props.channel.id === 1)" class="space-y-1">
                                    <label class="text-xs font-medium text-muted-foreground">Tiêu đề</label>
                                    <Input
                                        :model-value="item.title || ''"
                                        :disabled="!item.title || item.title.trim() === ''"
                                        placeholder="Tiêu đề..."
                                        class="text-sm"
                                        readonly
                                    />
                                </div>

                                <!-- Content -->
                                <div v-if="item.content" class="space-y-1">
                                    <label class="text-xs font-medium text-muted-foreground">Nội dung</label>
                                    <Input
                                        :model-value="item.content || ''"
                                        :disabled="!item.content || item.content.trim() === ''"
                                        placeholder="Nội dung..."
                                        class="text-sm"
                                        readonly
                                    />
                                </div>

                                <!-- Stats Row -->
                                <div class="grid grid-cols-3 gap-2 pt-2">
                                    <div class="text-center p-2 bg-red-50 rounded">
                                        <div class="flex items-center justify-center gap-1 text-red-600">
                                            <Heart class="h-3 w-3" />
                                            <span class="text-xs font-medium">{{ formatNumber(item.daily_like) }}</span>
                                        </div>
                                        <div class="text-xs text-muted-foreground">Thích</div>
                                    </div>
                                    <div class="text-center p-2 bg-blue-50 rounded">
                                        <div class="flex items-center justify-center gap-1 text-blue-600">
                                            <Share class="h-3 w-3" />
                                            <span class="text-xs font-medium">{{ formatNumber(item.daily_share) }}</span>
                                        </div>
                                        <div class="text-xs text-muted-foreground">Chia sẻ</div>
                                    </div>
                                    <div class="text-center p-2 bg-green-50 rounded">
                                        <div class="flex items-center justify-center gap-1 text-green-600">
                                            <MessageSquare class="h-3 w-3" />
                                            <span class="text-xs font-medium">{{ formatNumber(item.daily_comment) }}</span>
                                        </div>
                                        <div class="text-xs text-muted-foreground">Bình luận</div>
                                    </div>
                                </div>

                                <!-- Metadata -->
                                <div class="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                                    <div class="flex items-center gap-2">
                                        <Calendar class="h-3 w-3" />
                                        <span>{{ formatDate(item.created) }}</span>
                                    </div>
                                    <div class="font-medium text-primary">
                                        Tổng: {{ formatNumber(item.total) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Empty State -->
                        <div v-if="incidentData.length === 0 && !isLoading" class="text-center py-8 text-muted-foreground">
                            <Building class="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>Không tìm thấy dữ liệu sự kiện.</p>
                        </div>

                        <!-- Mobile Loading State -->
                        <div v-if="isLoading" class="space-y-3">
                            <div v-for="n in 3" :key="n" class="animate-pulse">
                                <div class="bg-card border rounded-lg p-4 space-y-3">
                                    <div class="flex justify-between">
                                        <div class="h-4 bg-gray-200 rounded w-16"></div>
                                        <div class="h-4 bg-gray-200 rounded w-20"></div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="h-3 bg-gray-200 rounded w-3/4"></div>
                                        <div class="h-8 bg-gray-200 rounded"></div>
                                        <div class="h-8 bg-gray-200 rounded"></div>
                                    </div>
                                    <div class="grid grid-cols-3 gap-2">
                                        <div class="h-12 bg-gray-200 rounded"></div>
                                        <div class="h-12 bg-gray-200 rounded"></div>
                                        <div class="h-12 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desktop: Table Layout -->
                <div class="hidden md:block">
                    <Table>
                    <TableHeader>
                        <TableRow>
                            <ResizableTableHeader
                                v-for="column in visibleColumns"
                                :key="column.key"
                                :column="column"
                                :sort-key="sortKey"
                                :sort-direction="sortDirection"
                                :width="getColumnWidth(column.key)"
                                :is-resizing="resizingColumn === column.key"
                                @sort="toggleSort"
                                @start-resize="startResize"
                                @drag-start="startDrag"
                                @drag-over="handleDragOver"
                                @drag-enter="handleDragEnter"
                                @drag-leave="handleDragLeave"
                                @drop="handleDrop"
                                @drag-end="handleDragEnd"
                            />
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow
                            v-for="(item, index) in incidentData"
                            :key="index"
                            class="hover:bg-muted/50 cursor-pointer transition-colors"
                            @click="handleRowClick(item, index)"
                        >
                            <!-- Dynamic cells based on column order -->
                            <TableCell
                                v-for="column in visibleColumns"
                                :key="column.key"
                                :class="[
                                    column.key === 'id' ? 'font-mono text-xs' : '',
                                    column.key === 'company' || column.key === 'source' || column.key === 'name_incident' ? 'text-sm' : '',
                                    ['daily_like', 'daily_share', 'daily_comment', 'total'].includes(column.key) ? 'text-right' : '',
                                    column.key === 'total' ? 'font-medium' : ''
                                ]"
                                :style="{ width: `${getColumnWidth(column.key)}px`, minWidth: `${getColumnWidth(column.key)}px`, maxWidth: `${getColumnWidth(column.key)}px` }"
                            >

                                <!-- Title Column -->
                                <template v-if="column.key === 'title'">
                                    <Input
                                        :model-value="item.title || ''"
                                        :disabled="!item.title || item.title.trim() === ''"
                                        placeholder="Tiêu đề..."
                                        class="w-full text-sm border-0 bg-transparent p-1 focus:bg-white focus:border-input"
                                        readonly
                                    />
                                </template>

                                <!-- Content Column -->
                                <template v-else-if="column.key === 'content'">
                                    <Input
                                        :model-value="item.content || ''"
                                        :disabled="!item.content || item.content.trim() === ''"
                                        placeholder="Nội dung..."
                                        class="w-full text-sm border-0 bg-transparent p-1 focus:bg-white focus:border-input"
                                        readonly
                                    />
                                </template>
                                <template v-else-if="column.key === 'url_post'">
                                    <Input
                                        :model-value="item.url_post || ''"
                                        placeholder="Nội dung..."
                                        class="w-full text-sm border-0 bg-transparent p-1 focus:bg-white focus:border-input"
                                        readonly
                                    />
                                </template>
                                <template v-else-if="column.key === 'reaction_flag'">
                                    <Badge variant="outline" class="text-xs">
                                        {{ item.reaction_flag || '—' }}
                                    </Badge>
                                </template>
                                <!-- Platform Column -->
                                <template v-else-if="column.key === 'platform'">
                                    <Badge variant="outline" class="text-xs">
                                        {{ item.platform || '—' }}
                                    </Badge>
                                </template>

                                <!-- Daily Likes Column -->
                                <template v-else-if="column.key === 'accumulated_like'">
                                    <div class="flex items-center justify-end gap-1">
                                        <span class="text-sm">{{ formatNumber(item.accumulated_like) }}</span>
                                    </div>
                                </template>

                                <!-- Daily Shares Column -->
                                <template v-else-if="column.key === 'accumulated_share'">
                                    <div class="flex items-center justify-end gap-1">
                                        <span class="text-sm">{{ formatNumber(item.accumulated_share) }}</span>
                                    </div>
                                </template>

                                <!-- Daily Comments Column -->
                                <template v-else-if="column.key === 'accumulated_comment'">
                                    <div class="flex items-center justify-end gap-1">
                                        <span class="text-sm">{{ formatNumber(item.accumulated_comment) }}</span>
                                    </div>
                                </template>

                                <!-- Created Column -->
                                <template v-else-if="column.key === 'created'">
                                    <div class="flex items-center gap-1 text-sm text-muted-foreground">
                                        <span>{{ formatDate(item.created) }}</span>
                                    </div>
                                </template>

                                <!-- Actions Column -->
                                <template v-else-if="column.key === 'actions'">
                                    <div class="flex items-center gap-1" @click.stop>
                                        <Button
                                            v-if="item.url_post"
                                            variant="ghost"
                                            size="sm"
                                            @click="handleOpenUrl(item.url_post)"
                                            class="h-8 w-8 p-0"
                                            title="Mở liên kết bài viết"
                                        >
                                            <ExternalLink class="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            @click="handleRowClick(item, index)"
                                            class="h-8 w-8 p-0"
                                            title="Xem chi tiết"
                                        >
                                            <Eye class="h-4 w-4" />
                                        </Button>
                                    </div>
                                </template>

                                <!-- Default Column (company, source, name_incident) -->
                                <template v-else>
                                    {{ getCellContent(item, column.key, index) }}
                                </template>
                            </TableCell>
                        </TableRow>

                        <!-- Loading Row -->
                        <TableRow v-if="isLoading">
                            <TableCell :colspan="visibleColumns.length" class="text-center py-8">
                                <RefreshCw class="h-6 w-6 animate-spin mx-auto mb-2" />
                                <p class="text-sm text-muted-foreground">Đang tải dữ liệu...</p>
                            </TableCell>
                        </TableRow>

                        <!-- Empty State -->
                        <TableRow v-if="!isLoading && incidentData.length === 0">
                            <TableCell :colspan="visibleColumns.length" class="text-center py-8 text-muted-foreground">
                                Không tìm thấy dữ liệu sự kiện.
                            </TableCell>
                        </TableRow>
                    </TableBody>
                    </Table>
                </div>
            </div>

            <!-- Pagination Controls -->
            <PaginationControls
                :current-page="currentPage"
                :total-pages="totalPages"
                :total-items="totalItems"
                :selected-items-per-page="selectedItemsPerPage"
                :has-next-page="hasNextPage"
                :has-prev-page="hasPrevPage"
                @go-to-page="goToPage"
                @next-page="nextPage"
                @prev-page="prevPage"
                @go-to-first-page="goToFirstPage"
                @go-to-last-page="goToLastPage"
            />
        </CardContent>
    </Card>

    <!-- Excel Export Dialog -->
    <ExcelExportDialog
        :is-open="showExportDialog"
        :is-exporting="isExporting"
        :export-columns="exportColumns"
        :channel-name="channel.name"
        :total-items="totalItems"
        @close="showExportDialog = false"
        @export="handleExport"
        @toggle-column="toggleExportColumn"
        @select-all="selectAllColumns"
        @deselect-all="deselectAllColumns"
    />

    <!-- Incident Detail Modal -->
    <IncidentDetailModal
        :is-open="showDetailModal"
        :item="selectedItem"
        :item-index="selectedItemIndex"
        :channel-name="channel.name"
        @close="handleCloseModal"
        @open-url="handleOpenUrl"
    />
</template>
