<script setup lang="ts">
import { nextTick, ref, watch, withDefaults } from 'vue';

interface Props {
    modelValue: string;
    isEditing: boolean;
    itemId: number; // Using array index as unique identifier
}

interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'save', value: string): void;
    (e: 'cancel'): void;
    (e: 'startEdit'): void;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    isEditing: true,
    itemId: 0
});
const emit = defineEmits<Emits>();

const editingValue = ref(props.modelValue);

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
    editingValue.value = newValue;
});

// Watch for editing state changes
watch(() => props.isEditing, async (isEditing) => {
    if (isEditing && props.itemId != null) {
        editingValue.value = props.modelValue;
        await nextTick();
        const textarea = document.querySelector(`[data-editor-id="${props.itemId}"] textarea`) as HTMLTextAreaElement;
        if (textarea) {
            textarea.focus();
            textarea.select();
        }
    }
});

const getDisplayContent = (content: string) => {
    if (!content) return '';

    // Show only first line (up to first line break or 100 characters)
    const firstLine = content.split('\n')[0];
    if (firstLine.length <= 100) {
        return firstLine;
    }

    return firstLine.substring(0, 100) + '...';
};

const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        emit('cancel');
    } else if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        emit('update:modelValue', editingValue.value);
        emit('save', editingValue.value);
    }
};

const handleSave = () => {
    emit('update:modelValue', editingValue.value);
    emit('save', editingValue.value);
};

const handleCancel = () => {
    editingValue.value = props.modelValue; // Reset to original value
    emit('cancel');
};

const handleStartEdit = () => {
    emit('startEdit');
};
</script>

<template>
    <div class="text-sm text-muted-foreground">
        <!-- Editing Mode -->
        <div
            v-if="isEditing"
            class="content-editor p-2 border-2 border-blue-200 rounded-md bg-blue-50/50"
            :data-editor-id="itemId"
        >
            <textarea
                v-model="editingValue"
                class="w-full min-h-[120px] p-3 text-sm border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                @keydown="handleKeydown"
                @click.stop
                placeholder="Nhập nội dung..."
            ></textarea>
            <div class="flex items-center justify-between mt-2 text-xs">
                <span class="text-muted-foreground">Nhấn Enter để lưu, Esc để hủy</span>
                <div class="flex gap-2">
                    <button
                        @click="handleCancel"
                        class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
                    >
                        Hủy
                    </button>
                    <button
                        @click="handleSave"
                        class="px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded"
                    >
                        Lưu
                    </button>
                </div>
            </div>
        </div>

        <!-- Display Mode -->
        <div
            v-else
            class="break-words whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer hover:bg-muted/50 p-2 rounded transition-colors border border-transparent hover:border-gray-200"
            @click="handleStartEdit"
            :title="modelValue"
        >
            {{ getDisplayContent(modelValue) || 'Nhấp để chỉnh sửa nội dung...' }}
        </div>
    </div>
</template>
