import { ref, reactive, onMounted, onUnmounted } from 'vue';

export interface ResizableColumn {
    key: string;
    width: number;
    minWidth: number;
    maxWidth?: number; // Optional, unlimited if not specified
}

export function useColumnResize(initialColumns: ResizableColumn[]) {
    const columnWidths = reactive<Record<string, number>>({});
    const isResizing = ref(false);
    const resizingColumn = ref<string | null>(null);
    const startX = ref(0);
    const startWidth = ref(0);

    // Initialize column widths
    onMounted(() => {
        initialColumns.forEach(col => {
            columnWidths[col.key] = col.width;
        });
    });

    const startResize = (event: MouseEvent, columnKey: string) => {
        event.preventDefault();
        event.stopPropagation();

        isResizing.value = true;
        resizingColumn.value = columnKey;
        startX.value = event.clientX;
        startWidth.value = columnWidths[columnKey] || 150;

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', stopResize);
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    };

    const handleMouseMove = (event: MouseEvent) => {
        if (!isResizing.value || !resizingColumn.value) return;

        const deltaX = event.clientX - startX.value;
        const column = initialColumns.find(col => col.key === resizingColumn.value);
        const minWidth = column?.minWidth || 80;
        const maxWidth = column?.maxWidth; // Can be undefined for unlimited

        let newWidth = startWidth.value + deltaX;

        // Apply minimum width constraint
        newWidth = Math.max(minWidth, newWidth);

        // Apply maximum width constraint only if specified
        if (maxWidth !== undefined) {
            newWidth = Math.min(maxWidth, newWidth);
        }

        columnWidths[resizingColumn.value] = newWidth;
    };

    const stopResize = () => {
        isResizing.value = false;
        resizingColumn.value = null;

        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', stopResize);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    };

    const getColumnWidth = (columnKey: string) => {
        return columnWidths[columnKey] || 150;
    };

    const resetColumnWidths = () => {
        initialColumns.forEach(col => {
            columnWidths[col.key] = col.width;
        });
    };

    // Cleanup on unmount
    onUnmounted(() => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', stopResize);
    });

    return {
        columnWidths,
        isResizing,
        resizingColumn,
        startResize,
        getColumnWidth,
        resetColumnWidths,
    };
}
