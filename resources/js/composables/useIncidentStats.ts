import { ref, computed } from 'vue';

export interface ChannelStats {
    channelId: string;
    channelName: string;
    totalPosts: number;
    reactionStats: {
        label: string;
        count: number;
    }[];
}

export interface IncidentStatsResponse {
    success: boolean;
    data: ChannelStats[];
    error?: string;
}

export function useIncidentStats() {
    const isLoading = ref(false);
    const error = ref<string>('');
    const channelStats = ref<ChannelStats[]>([]);

    const fetchChannelStats = async (channels: any[]) => {
        if (!channels || channels.length === 0) return;

        isLoading.value = true;
        error.value = '';

        try {
            const token = route().params.token ?? '';

            const promises = channels.map(async (channel) => {
                // Fetch total posts count for this channel
                const totalPostsResponse = await fetch(`/api/incident-data/stats/total-posts/${channel.id}?token=${token}`);
                const totalPostsData = await totalPostsResponse.json();

                // Fetch reaction flag statistics for this channel
                const reactionStatsResponse = await fetch(`/api/incident-data/stats/reactions/${channel.id}?token=${token}`);
                const reactionStatsData = await reactionStatsResponse.json();

                return {
                    channelId: channel.id,
                    channelName: channel.name,
                    totalPosts: totalPostsData.success ? totalPostsData.data.total : 0,
                    reactionStats: reactionStatsData.success ? reactionStatsData.data : []
                };
            });

            const results = await Promise.all(promises);
            channelStats.value = results;

        } catch (err) {
            error.value = 'Lỗi khi tải thống kê dữ liệu';
            console.error('Error fetching incident stats:', err);
        } finally {
            isLoading.value = false;
        }
    };

    // Get total posts across all channels
    const totalPostsAllChannels = computed(() => {
        return channelStats.value.reduce((sum, channel) => sum + channel.totalPosts, 0);
    });

    // Get aggregated reaction stats across all channels
    const aggregatedReactionStats = computed(() => {
        const reactionMap = new Map<string, number>();

        channelStats.value.forEach(channel => {
            channel.reactionStats.forEach(reaction => {
                const currentCount = reactionMap.get(reaction.label) || 0;
                reactionMap.set(reaction.label, currentCount + reaction.count);
            });
        });

        return Array.from(reactionMap.entries()).map(([label, count]) => ({
            label,
            count
        }));
    });

    // Get stats for a specific channel
    const getChannelStats = (channelId: string) => {
        return channelStats.value.find(stats => stats.channelId === channelId);
    };

    // Format number for display
    const formatNumber = (num: number) => {
        return new Intl.NumberFormat('vi-VN').format(num);
    };

    // Get reaction stats summary for a channel
    const getChannelReactionSummary = (channelId: string) => {
        const stats = getChannelStats(channelId);
        if (!stats || stats.reactionStats.length === 0) return 'Chưa có phản ứng';

        const totalReactions = stats.reactionStats.reduce((sum, reaction) => sum + reaction.count, 0);
        const topReaction = stats.reactionStats.reduce((max, reaction) =>
            reaction.count > max.count ? reaction : max
        );

        return `${formatNumber(totalReactions)} phản ứng (${topReaction.label}: ${formatNumber(topReaction.count)})`;
    };

    return {
        // State
        isLoading,
        error,
        channelStats,

        // Computed
        totalPostsAllChannels,
        aggregatedReactionStats,

        // Methods
        fetchChannelStats,
        getChannelStats,
        getChannelReactionSummary,
        formatNumber,
    };
}
