import { type LandReport, type DashboardStats, type ChartDataPoint } from '@/types';

export function useDashboardData() {
    const landReports: LandReport[] = [
        {
            id: 'land-1',
            title: 'Prime Agricultural Land - Sector 45',
            location: 'Gurgaon, Haryana',
            area: 25.5,
            landType: 'agricultural',
            value: 12500000,
            status: 'available',
            lastUpdated: '2024-01-15T10:30:00Z',
            coordinates: { lat: 28.4595, lng: 77.0266 },
            description: 'Fertile agricultural land with water access and good connectivity',
            images: ['https://picsum.photos/400/300?random=1', 'https://picsum.photos/400/300?random=2']
        },
        {
            id: 'land-2',
            title: 'Residential Plot - Green Valley',
            location: 'Noida, Uttar Pradesh',
            area: 0.5,
            landType: 'residential',
            value: 8500000,
            status: 'sold',
            lastUpdated: '2024-01-14T15:20:00Z',
            coordinates: { lat: 28.5355, lng: 77.3910 },
            description: 'Premium residential plot in gated community',
            images: ['https://picsum.photos/400/300?random=3']
        },
        {
            id: 'land-3',
            title: 'Commercial Space - IT Hub',
            location: 'Bangalore, Karnataka',
            area: 2.8,
            landType: 'commercial',
            value: 45000000,
            status: 'pending',
            lastUpdated: '2024-01-13T09:45:00Z',
            coordinates: { lat: 12.9716, lng: 77.5946 },
            description: 'Strategic commercial land in prime IT corridor',
            images: ['https://picsum.photos/400/300?random=4', 'https://picsum.photos/400/300?random=5']
        },
        {
            id: 'land-4',
            title: 'Industrial Plot - Manufacturing Zone',
            location: 'Pune, Maharashtra',
            area: 15.2,
            landType: 'industrial',
            value: 28000000,
            status: 'available',
            lastUpdated: '2024-01-12T14:10:00Z',
            coordinates: { lat: 18.5204, lng: 73.8567 },
            description: 'Large industrial plot with excellent logistics connectivity',
            images: ['https://picsum.photos/400/300?random=6']
        },
        {
            id: 'land-5',
            title: 'Forest Land - Conservation Area',
            location: 'Dehradun, Uttarakhand',
            area: 50.0,
            landType: 'forest',
            value: 15000000,
            status: 'reserved',
            lastUpdated: '2024-01-11T11:30:00Z',
            coordinates: { lat: 30.3165, lng: 78.0322 },
            description: 'Protected forest area for conservation projects',
            images: ['https://picsum.photos/400/300?random=7', 'https://picsum.photos/400/300?random=8']
        },
        {
            id: 'land-6',
            title: 'Luxury Villa Plot - Hill Station',
            location: 'Shimla, Himachal Pradesh',
            area: 1.2,
            landType: 'residential',
            value: 18500000,
            status: 'available',
            lastUpdated: '2024-01-10T16:45:00Z',
            coordinates: { lat: 31.1048, lng: 77.1734 },
            description: 'Premium hill station plot with panoramic valley views',
            images: ['https://picsum.photos/400/300?random=9']
        },
        {
            id: 'land-7',
            title: 'Organic Farm Land - Certified',
            location: 'Nashik, Maharashtra',
            area: 35.8,
            landType: 'agricultural',
            value: 22000000,
            status: 'pending',
            lastUpdated: '2024-01-09T08:15:00Z',
            coordinates: { lat: 19.9975, lng: 73.7898 },
            description: 'Certified organic farmland with modern irrigation systems',
            images: ['https://picsum.photos/400/300?random=10', 'https://picsum.photos/400/300?random=11']
        },
        {
            id: 'land-8',
            title: 'Tech Park Development Site',
            location: 'Hyderabad, Telangana',
            area: 8.5,
            landType: 'commercial',
            value: 65000000,
            status: 'sold',
            lastUpdated: '2024-01-08T13:20:00Z',
            coordinates: { lat: 17.3850, lng: 78.4867 },
            description: 'Prime location for tech park development in HITEC City',
            images: ['https://picsum.photos/400/300?random=12']
        }
    ];

    const dashboardStats: DashboardStats = {
        totalLands: landReports.length,
        totalValue: landReports.reduce((sum, land) => sum + land.value, 0),
        availableLands: landReports.filter(land => land.status === 'available').length,
        soldLands: landReports.filter(land => land.status === 'sold').length,
        pendingDeals: landReports.filter(land => land.status === 'pending').length,
        monthlyGrowth: 12.5
    };

    const landTypeDistribution: ChartDataPoint[] = [
        {
            label: 'Agricultural',
            value: landReports.filter(land => land.landType === 'agricultural').length,
            color: '#10b981'
        },
        {
            label: 'Residential',
            value: landReports.filter(land => land.landType === 'residential').length,
            color: '#3b82f6'
        },
        {
            label: 'Commercial',
            value: landReports.filter(land => land.landType === 'commercial').length,
            color: '#f59e0b'
        },
        {
            label: 'Industrial',
            value: landReports.filter(land => land.landType === 'industrial').length,
            color: '#ef4444'
        },
        {
            label: 'Forest',
            value: landReports.filter(land => land.landType === 'forest').length,
            color: '#22c55e'
        }
    ];

    const statusDistribution: ChartDataPoint[] = [
        {
            label: 'Available',
            value: landReports.filter(land => land.status === 'available').length,
            color: '#10b981'
        },
        {
            label: 'Sold',
            value: landReports.filter(land => land.status === 'sold').length,
            color: '#6366f1'
        },
        {
            label: 'Pending',
            value: landReports.filter(land => land.status === 'pending').length,
            color: '#f59e0b'
        },
        {
            label: 'Reserved',
            value: landReports.filter(land => land.status === 'reserved').length,
            color: '#8b5cf6'
        }
    ];

    const monthlyRevenue: ChartDataPoint[] = [
        { label: 'Jan', value: 45000000 },
        { label: 'Feb', value: 52000000 },
        { label: 'Mar', value: 48000000 },
        { label: 'Apr', value: 61000000 },
        { label: 'May', value: 55000000 },
        { label: 'Jun', value: 67000000 },
        { label: 'Jul', value: 72000000 },
        { label: 'Aug', value: 58000000 },
        { label: 'Sep', value: 64000000 },
        { label: 'Oct', value: 69000000 },
        { label: 'Nov', value: 75000000 },
        { label: 'Dec', value: 82000000 }
    ];

    const getLandReports = () => landReports;
    
    const getDashboardStats = () => dashboardStats;
    
    const getLandTypeDistribution = () => landTypeDistribution;
    
    const getStatusDistribution = () => statusDistribution;
    
    const getMonthlyRevenue = () => monthlyRevenue;
    
    const getRecentLands = (limit: number = 5) => 
        landReports
            .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())
            .slice(0, limit);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    };

    const formatArea = (area: number) => {
        return `${area.toFixed(1)} hectares`;
    };

    return {
        getLandReports,
        getDashboardStats,
        getLandTypeDistribution,
        getStatusDistribution,
        getMonthlyRevenue,
        getRecentLands,
        formatCurrency,
        formatArea
    };
}
