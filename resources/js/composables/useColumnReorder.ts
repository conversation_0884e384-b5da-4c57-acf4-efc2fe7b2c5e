import { ref, reactive } from 'vue';

export interface DraggableColumn {
    key: string;
    label: string;
    order: number;
}

export function useColumnReorder(initialColumns: DraggableColumn[]) {
    const columnOrder = reactive<Record<string, number>>({});
    const isDragging = ref(false);
    const draggedColumn = ref<string | null>(null);
    const dragOverColumn = ref<string | null>(null);

    // Initialize column order
    initialColumns.forEach(col => {
        columnOrder[col.key] = col.order;
    });

    const startDrag = (event: DragEvent, columnKey: string) => {
        if (!event.dataTransfer) return;
        
        isDragging.value = true;
        draggedColumn.value = columnKey;
        
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', columnKey);
        
        // Add visual feedback
        if (event.target instanceof HTMLElement) {
            event.target.style.opacity = '0.5';
        }
    };

    const handleDragOver = (event: DragEvent, columnKey: string) => {
        event.preventDefault();
        if (!event.dataTransfer) return;
        
        event.dataTransfer.dropEffect = 'move';
        dragOverColumn.value = columnKey;
    };

    const handleDragEnter = (event: DragEvent, columnKey: string) => {
        event.preventDefault();
        dragOverColumn.value = columnKey;
    };

    const handleDragLeave = (event: DragEvent) => {
        // Only clear if we're leaving the entire drop zone
        if (!event.relatedTarget || !(event.currentTarget as HTMLElement).contains(event.relatedTarget as Node)) {
            dragOverColumn.value = null;
        }
    };

    const handleDrop = (event: DragEvent, targetColumnKey: string) => {
        event.preventDefault();
        
        const sourceColumnKey = draggedColumn.value;
        if (!sourceColumnKey || sourceColumnKey === targetColumnKey) {
            resetDragState();
            return;
        }

        // Get current orders
        const sourceOrder = columnOrder[sourceColumnKey];
        const targetOrder = columnOrder[targetColumnKey];

        // Swap the orders
        columnOrder[sourceColumnKey] = targetOrder;
        columnOrder[targetColumnKey] = sourceOrder;

        resetDragState();
    };

    const handleDragEnd = (event: DragEvent) => {
        // Reset visual feedback
        if (event.target instanceof HTMLElement) {
            event.target.style.opacity = '';
        }
        resetDragState();
    };

    const resetDragState = () => {
        isDragging.value = false;
        draggedColumn.value = null;
        dragOverColumn.value = null;
    };

    const getColumnOrder = (columnKey: string) => {
        return columnOrder[columnKey] || 0;
    };

    const resetColumnOrder = () => {
        initialColumns.forEach(col => {
            columnOrder[col.key] = col.order;
        });
    };

    const getOrderedColumns = (columns: any[]) => {
        return [...columns].sort((a, b) => {
            const orderA = columnOrder[a.key] || 0;
            const orderB = columnOrder[b.key] || 0;
            return orderA - orderB;
        });
    };

    return {
        columnOrder,
        isDragging,
        draggedColumn,
        dragOverColumn,
        startDrag,
        handleDragOver,
        handleDragEnter,
        handleDragLeave,
        handleDrop,
        handleDragEnd,
        getColumnOrder,
        resetColumnOrder,
        getOrderedColumns,
    };
}
