import { ref } from 'vue';
import type { TableColumn } from '@/config/tableColumns';

export interface ExportColumn {
    key: string;
    label: string;
    selected: boolean;
}

export function useExcelExport() {
    const isExporting = ref(false);
    const showExportDialog = ref(false);
    const exportColumns = ref<ExportColumn[]>([]);

    // Initialize export columns from table columns
    const initializeExportColumns = (tableColumns: TableColumn[], hiddenColumns: Set<string>, channelId?: string | number) => {
        exportColumns.value = tableColumns
            .filter(col => {
                // Filter out hidden columns and conditional columns
                if (hiddenColumns.has(col.key)) return false;
                if (col.key === 'title' && (channelId === '1' || channelId === 1)) return false;
                if (col.key === 'actions') return false; // Don't export actions
                return true;
            })
            .map(col => ({
                key: col.key,
                label: col.label,
                selected: true // Default to selected
            }));
    };

    // Convert data to CSV format
    const convertToCSV = (data: any[], columns: ExportColumn[]) => {
        const selectedColumns = columns.filter(col => col.selected);

        // Create header row
        const headers = selectedColumns.map(col => `"${col.label}"`).join(',');

        // Create data rows (preserve all data including HTML)
        const rows = data.map(item => {
            return selectedColumns.map(col => {
                let value = getCellValue(item, col.key);

                // Handle special formatting while preserving HTML
                if (typeof value === 'string') {
                    // Escape quotes and wrap in quotes, preserve HTML as-is
                    value = `"${value.replace(/"/g, '""')}"`;
                } else if (typeof value === 'number') {
                    value = value.toString();
                } else {
                    // Convert to string and preserve any HTML content
                    value = `"${String(value || '')}"`;
                }

                return value;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    };

    // Get cell value based on column key (preserves HTML content)
    const getCellValue = (item: any, columnKey: string) => {
        switch (columnKey) {
            case 'id':
                return item.id || '';
            case 'title':
                // Preserve HTML content as-is
                return item.title || '';
            case 'content':
                // Preserve HTML content as-is
                return item.content || '';
            case 'company':
                return item.company || '';
            case 'platform':
                return item.platform || '';
            case 'source':
                return item.source || '';
            case 'accumulated_like':
                return item.accumulated_like || 0;
            case 'accumulated_share':
                return item.accumulated_share || 0;
            case 'accumulated_comment':
                return item.accumulated_comment || 0;
            case 'total':
                return item.total || 0;
            case 'created':
                return item.created ? new Date(item.created).toLocaleDateString('vi-VN') : '';
            case 'name_incident':
                return item.name_incident || '';
            default:
                // Return raw value for any other column, preserving HTML
                return item[columnKey] || '';
        }
    };

    // Download CSV file
    const downloadCSV = (csvContent: string, filename: string) => {
        const BOM = '\uFEFF'; // UTF-8 BOM for proper Vietnamese character display
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    };

    // Generate Excel-compatible HTML format
    const convertToExcelHTML = (data: any[], columns: ExportColumn[]) => {
        const selectedColumns = columns.filter(col => col.selected);

        let html = `
            <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
            <head>
                <meta charset="utf-8">
                <meta name="ProgId" content="Excel.Sheet">
                <meta name="Generator" content="Microsoft Excel 15">
                <!--[if gte mso 9]>
                <xml>
                    <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                            <x:ExcelWorksheet>
                                <x:Name>Sheet1</x:Name>
                                <x:WorksheetOptions>
                                    <x:DisplayGridlines/>
                                </x:WorksheetOptions>
                            </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                    </x:ExcelWorkbook>
                </xml>
                <![endif]-->
                <style>
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .number { text-align: right; }
                </style>
            </head>
            <body>
                <table>
                    <thead>
                        <tr>
        `;

        // Add headers
        selectedColumns.forEach(col => {
            html += `<th>${col.label}</th>`;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add data rows (preserve all data including HTML)
        data.forEach(item => {
            html += '<tr>';
            selectedColumns.forEach(col => {
                const value = getCellValue(item, col.key);
                const isNumber = typeof value === 'number';
                // Preserve HTML content in Excel cells
                html += `<td${isNumber ? ' class="number"' : ''}>${value}</td>`;
            });
            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </body>
            </html>
        `;

        return html;
    };

    // Download Excel file
    const downloadExcel = (htmlContent: string, filename: string) => {
        const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    };

    // Main export function (preserves table sort order and HTML content)
    const exportToExcel = async (data: any[], channelName: string, format: 'csv' | 'excel' = 'excel') => {
        isExporting.value = true;

        try {
            const selectedColumns = exportColumns.value.filter(col => col.selected);

            if (selectedColumns.length === 0) {
                throw new Error('Vui lòng chọn ít nhất một cột để xuất');
            }

            // Data is already sorted from the table, so we use it as-is
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `${channelName}_${timestamp}.${format === 'csv' ? 'csv' : 'xls'}`;

            if (format === 'csv') {
                const csvContent = convertToCSV(data, selectedColumns);
                downloadCSV(csvContent, filename);
            } else {
                const htmlContent = convertToExcelHTML(data, selectedColumns);
                downloadExcel(htmlContent, filename);
            }

            showExportDialog.value = false;
        } catch (error) {
            console.error('Export error:', error);
            throw error;
        } finally {
            isExporting.value = false;
        }
    };

    // Toggle column selection
    const toggleColumn = (columnKey: string) => {
        const column = exportColumns.value.find(col => col.key === columnKey);
        if (column) {
            column.selected = !column.selected;
        }
    };

    // Select all columns
    const selectAllColumns = () => {
        exportColumns.value.forEach(col => {
            col.selected = true;
        });
    };

    // Deselect all columns
    const deselectAllColumns = () => {
        exportColumns.value.forEach(col => {
            col.selected = false;
        });
    };

    // Open export dialog
    const openExportDialog = (tableColumns: TableColumn[], hiddenColumns: Set<string>, channelId?: string | number) => {
        initializeExportColumns(tableColumns, hiddenColumns, channelId);
        showExportDialog.value = true;
    };

    return {
        isExporting,
        showExportDialog,
        exportColumns,
        exportToExcel,
        toggleColumn,
        selectAllColumns,
        deselectAllColumns,
        openExportDialog,
    };
}
