import { computed, ref, watch } from 'vue';
import type { IncidentData } from '@/types';

export function useIncidentDataTable() {
    // State
    const allIncidentData = ref<IncidentData[]>([]);
    const isLoading = ref(false);
    const error = ref('');
    const searchQuery = ref('');
    const sortKey = ref<string>('created');
    const sortDirection = ref<'asc' | 'desc'>('desc');

    // Pagination
    const itemsPerPageOptions = [10, 20, 50, 100, 200, 'all'] as const;
    const selectedItemsPerPage = ref<number | 'all'>(100);
    const currentPage = ref(1);

    // Column visibility
    const hiddenColumns = ref<Set<string>>(new Set());



    // Computed items per page
    const ITEMS_PER_PAGE = computed(() => {
        return selectedItemsPerPage.value === 'all' ? totalItems.value : selectedItemsPerPage.value;
    });

    // Utility function to get nested object values
    const getNestedValue = (obj: any, path: string) => {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    // Filtered and sorted data
    const filteredAndSortedData = computed(() => {
        let result = [...allIncidentData.value];

        // Apply search filter
        if (searchQuery.value.trim()) {
            const query = searchQuery.value.toLowerCase().trim();
            result = result.filter(item => {
                return (
                    item.title?.toLowerCase().includes(query) ||
                    item.content?.toLowerCase().includes(query) ||
                    item.company?.toLowerCase().includes(query) ||
                    item.platform?.toLowerCase().includes(query) ||
                    item.source?.toLowerCase().includes(query) ||
                    item.name_incident?.toLowerCase().includes(query)
                );
            });
        }

        // Apply sorting
        if (sortKey.value) {
            result.sort((a, b) => {
                const aValue = getNestedValue(a, sortKey.value);
                const bValue = getNestedValue(b, sortKey.value);

                // Handle different data types
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return sortDirection.value === 'asc' ? aValue - bValue : bValue - aValue;
                }

                if (aValue instanceof Date && bValue instanceof Date) {
                    return sortDirection.value === 'asc'
                        ? aValue.getTime() - bValue.getTime()
                        : bValue.getTime() - aValue.getTime();
                }

                // String comparison
                const aStr = String(aValue || '').toLowerCase();
                const bStr = String(bValue || '').toLowerCase();

                if (sortDirection.value === 'asc') {
                    return aStr.localeCompare(bStr);
                } else {
                    return bStr.localeCompare(aStr);
                }
            });
        }

        return result;
    });

    // Paginated display data
    const incidentData = computed(() => {
        if (selectedItemsPerPage.value === 'all') {
            return filteredAndSortedData.value;
        }
        const startIndex = (currentPage.value - 1) * ITEMS_PER_PAGE.value;
        const endIndex = startIndex + ITEMS_PER_PAGE.value;
        return filteredAndSortedData.value.slice(startIndex, endIndex);
    });

    // Pagination computed properties
    const totalItems = computed(() => filteredAndSortedData.value.length);
    const totalPages = computed(() => {
        if (selectedItemsPerPage.value === 'all') return 1;
        return Math.ceil(totalItems.value / ITEMS_PER_PAGE.value);
    });
    const hasNextPage = computed(() => currentPage.value < totalPages.value);
    const hasPrevPage = computed(() => currentPage.value > 1);
    const startItem = computed(() => {
        if (selectedItemsPerPage.value === 'all') return 1;
        return (currentPage.value - 1) * ITEMS_PER_PAGE.value + 1;
    });
    const endItem = computed(() => {
        if (selectedItemsPerPage.value === 'all') return totalItems.value;
        return Math.min(currentPage.value * ITEMS_PER_PAGE.value, totalItems.value);
    });

    // Fetch data function
    const fetchData = async (channelId: string, page = 1, append = false) => {
        if (!channelId) return;

        isLoading.value = true;
        error.value = '';
        const token = route().params.token ?? '';

        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: '1000',
                token: token// Fetch more data at once
            });

            const response = await fetch(`/api/incident-data/channel/${channelId}?${params}`);

            if (!response.ok) {
                throw new Error('Failed to fetch data');
            }

            const result = await response.json();

            if (result.success) {
                if (append) {
                    allIncidentData.value = [...allIncidentData.value, ...result.data];
                } else {
                    allIncidentData.value = result.data;
                }

                // If there's more data, fetch it
                if (result.pagination.hasMore) {
                    await fetchData(channelId, page + 1, true);
                }
            } else {
                error.value = result.error || 'Failed to fetch data';
            }
        } catch (err) {
            error.value = 'Network error occurred';
            console.error('Error fetching incident data:', err);
        } finally {
            isLoading.value = false;
        }
    };

    // Actions
    const refreshData = (channelId: string) => {
        currentPage.value = 1;
        fetchData(channelId, 1, false);
    };

    const clearSearch = () => {
        searchQuery.value = '';
        currentPage.value = 1;
    };

    const toggleSort = (columnKey: string) => {
        if (sortKey.value === columnKey) {
            sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
        } else {
            sortKey.value = columnKey;
            sortDirection.value = 'asc';
        }
        currentPage.value = 1;
    };

    const changeItemsPerPage = (value: string) => {
        selectedItemsPerPage.value = value === 'all' ? 'all' : parseInt(value);
        currentPage.value = 1;
    };

    // Pagination functions
    const goToPage = (page: number) => {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
        }
    };

    const nextPage = () => {
        if (hasNextPage.value) {
            currentPage.value += 1;
        }
    };

    const prevPage = () => {
        if (hasPrevPage.value) {
            currentPage.value -= 1;
        }
    };

    const goToFirstPage = () => {
        currentPage.value = 1;
    };

    const goToLastPage = () => {
        currentPage.value = totalPages.value;
    };

    // Column visibility functions
    const toggleColumn = (columnKey: string) => {
        if (hiddenColumns.value.has(columnKey)) {
            hiddenColumns.value.delete(columnKey);
        } else {
            hiddenColumns.value.add(columnKey);
        }
    };

    const showAllColumns = () => {
        hiddenColumns.value.clear();
    };

    const hideAllColumns = () => {
        const hideableColumns = ['id', 'company', 'platform', 'source', 'daily_like', 'daily_share', 'daily_comment', 'total', 'created', 'name_incident'];
        hideableColumns.forEach(col => {
            hiddenColumns.value.add(col);
        });
    };



    // Watch for search changes to reset to first page
    watch(searchQuery, () => {
        currentPage.value = 1;
    });

    return {
        // State
        allIncidentData,
        incidentData,
        filteredAndSortedData,
        isLoading,
        error,
        searchQuery,
        sortKey,
        sortDirection,
        selectedItemsPerPage,
        itemsPerPageOptions,
        currentPage,
        hiddenColumns,

        // Computed
        totalItems,
        totalPages,
        hasNextPage,
        hasPrevPage,
        startItem,
        endItem,
        ITEMS_PER_PAGE,

        // Actions
        fetchData,
        refreshData,
        clearSearch,
        toggleSort,
        changeItemsPerPage,
        goToPage,
        nextPage,
        prevPage,
        goToFirstPage,
        goToLastPage,
        toggleColumn,
        showAllColumns,
        hideAllColumns,
    };
}
