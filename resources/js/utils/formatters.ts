export const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

export const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
};

export const openUrl = (url?: string) => {
    if (url) {
        window.open(url, '_blank');
    }
};
