import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
}

export type AppPageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
};

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;

export interface Channel {
    id: string;
    name: string;
    token?: string;
}

export interface IncidentData {
    id: number;
    report_date: string;
    created: string;
    company: string;
    platform: string;
    channel_id: string;
    source: string;
    title: string;
    content: string;
    url_profile?: string;
    url_post?: string;
    es_id?: string;
    reaction_flag?: string;
    daily_like: number;
    daily_share: number;
    daily_comment: number;
    accumulated_like: number;
    accumulated_share: number;
    accumulated_comment: number;
    total: number;
    id_config?: string;
    name_incident?: string;
}

export interface ChannelItem {
    id: string;
    title: string;
    description?: string;
    url?: string;
    publishedAt: string;
    author?: string;
    tags?: string[];
    thumbnail?: string;
}

export interface LandReport {
    id: string;
    title: string;
    location: string;
    area: number; // in hectares
    landType: 'agricultural' | 'residential' | 'commercial' | 'industrial' | 'forest';
    value: number; // in currency
    status: 'available' | 'sold' | 'pending' | 'reserved';
    lastUpdated: string;
    coordinates?: {
        lat: number;
        lng: number;
    };
    description?: string;
    images?: string[];
}

export interface DashboardStats {
    totalLands: number;
    totalValue: number;
    availableLands: number;
    soldLands: number;
    pendingDeals: number;
    monthlyGrowth: number;
}

export interface ChartDataPoint {
    label: string;
    value: number;
    color?: string;
}
