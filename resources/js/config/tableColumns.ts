export interface TableColumn {
    key: string;
    label: string;
    sortable: boolean;
    hideable: boolean;
    width?: string;
    resizable?: boolean;
    minWidth?: number;
    maxWidth?: number;
    order?: number;
    moveable?: boolean;
}

export const incidentDataColumns: TableColumn[] = [
    // { key: 'id', label: 'ID', sortable: true, hideable: true, width: '80px', resizable: true, minWidth: 60, order: 1, moveable: true },
    { key: 'title', label: 'Tiêu đề', sortable: true, hideable: false, width: '250px', resizable: true, minWidth: 200, order: 2, moveable: true },
    { key: 'content', label: 'Nội dung', sortable: true, hideable: false, width: '300px', resizable: true, minWidth: 200, order: 3, moveable: true },
    { key: 'url_post', label: 'Post URL', sortable: true, hideable: false, width: '300px', resizable: true, minWidth: 200, order: 3, moveable: true },
    { key: 'reaction_flag', label: 'Reaction', sortable: true, hideable: false, width: '150px', resizable: true, minWidth: 200, order: 3, moveable: true },
    { key: 'company', label: 'Công ty', sortable: true, hideable: true, width: '150px', resizable: true, minWidth: 100, order: 4, moveable: true },
    { key: 'platform', label: 'Nền tảng', sortable: true, hideable: true, width: '120px', resizable: true, minWidth: 80, order: 5, moveable: true },
    { key: 'source', label: 'Nguồn', sortable: true, hideable: true, width: '100px', resizable: true, minWidth: 80, order: 6, moveable: true },
    { key: 'daily_like', label: 'Thích', sortable: true, hideable: true, width: '100px', resizable: true, minWidth: 120, order: 7, moveable: true },
    { key: 'daily_share', label: 'Chia sẻ', sortable: true, hideable: true, width: '100px', resizable: true, minWidth: 130, order: 8, moveable: true },
    { key: 'daily_comment', label: 'Bình luận', sortable: true, hideable: true, width: '120px', resizable: true, minWidth: 120, order: 9, moveable: true },
    { key: 'created', label: 'Ngày tạo', sortable: true, hideable: true, width: '150px', resizable: true, minWidth: 120, order: 11, moveable: true },
];

export const getSortIcon = (columnKey: string, sortKey: string, sortDirection: 'asc' | 'desc') => {
    if (sortKey !== columnKey) return 'ArrowUpDown';
    return sortDirection === 'asc' ? 'ArrowUp' : 'ArrowDown';
};
