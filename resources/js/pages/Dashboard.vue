<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import StatsCard from '@/components/StatsCard.vue';
import StatsCardSkeleton from '@/components/StatsCardSkeleton.vue';
import { useDashboardData } from '@/composables/useDashboardData';
import { useIncidentStats } from '@/composables/useIncidentStats';
import { computed, onMounted } from 'vue';
import {
    MapPin,
    DollarSign,
    Building,
    CheckCircle,
    MessageSquare,
    Heart,
    Share,
    Eye,
    TrendingUp,
    Activity
} from 'lucide-vue-next';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

const {
    getDashboardStats,
    formatCurrency
} = useDashboardData();

const stats = computed(() => getDashboardStats());

// Props
interface Props {
    channels: any[];
    event: string;
}

const props = defineProps<Props>();

// Incident data statistics
const {
    isLoading: isStatsLoading,
    error: statsError,
    channelStats,
    totalPostsAllChannels,
    aggregatedReactionStats,
    fetchChannelStats,
    getChannelStats,
    getChannelReactionSummary,
    formatNumber,
} = useIncidentStats();

// Fetch stats when component mounts
onMounted(() => {
    if (props.channels && props.channels.length > 0) {
        fetchChannelStats(props.channels);
    }
});

// Computed stats for display
const totalChannels = computed(() => props.channels?.length || 0);
const totalReactions = computed(() => {
    return aggregatedReactionStats.value.reduce((sum, reaction) => sum + reaction.count, 0);
});
const topReaction = computed(() => {
    if (aggregatedReactionStats.value.length === 0) return null;
    return aggregatedReactionStats.value.reduce((max, reaction) =>
        reaction.count > max.count ? reaction : max
    );
});

</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
            <h4 class="text-xl font-bold tracking-tight">Tổng quan các kênh có sự vụ: {{ event }}</h4>

            <!-- Error Message -->
            <div v-if="statsError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                {{ statsError }}
            </div>

            <!-- Overall Stats Cards -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <!-- Total Channels -->
                <StatsCard
                    title="Tổng số kênh"
                    :value="totalChannels.toString()"
                    description="Kênh đang theo dõi"
                    color="blue"
                    :icon="Building"
                />

                <!-- Total Posts -->
                <template v-if="isStatsLoading">
                    <StatsCardSkeleton />
                </template>
                <template v-else>
                    <StatsCard
                        title="Tổng bài viết"
                        :value="formatNumber(totalPostsAllChannels)"
                        description="Bài viết trên tất cả kênh"
                        color="green"
                        :icon="MessageSquare"
                    />
                </template>

                <!-- Total Reactions -->
                <template v-if="isStatsLoading">
                    <StatsCardSkeleton />
                </template>
                <template v-else>
                    <StatsCard
                        title="Tổng phản ứng"
                        :value="formatNumber(totalReactions)"
                        description="Phản ứng trên tất cả bài viết"
                        color="purple"
                        :icon="Heart"
                    />
                </template>

                <!-- Top Reaction -->
                <template v-if="isStatsLoading">
                    <StatsCardSkeleton />
                </template>
                <template v-else>
                    <StatsCard
                        :title="topReaction ? `Phản ứng hàng đầu: ${topReaction.label}` : 'Chưa có phản ứng'"
                        :value="topReaction ? formatNumber(topReaction.count) : '0'"
                        description="Phản ứng phổ biến nhất"
                        color="orange"
                        :icon="TrendingUp"
                    />
                </template>
            </div>

            <!-- Individual Channel Stats -->
            <div class="mt-6">
                <h5 class="text-lg font-semibold mb-4">Thống kê từng kênh</h5>
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <template v-if="isStatsLoading">
                        <StatsCardSkeleton v-for="n in channels.length" :key="n" />
                    </template>
                    <template v-else>
                        <StatsCard
                            v-for="channel in channels"
                            :key="channel.id"
                            :title="channel.name"
                            :value="formatNumber(getChannelStats(channel.id)?.totalPosts || 0)"
                            :description="getChannelReactionSummary(channel.id)"
                            color="blue"
                            :icon="Activity"
                        />
                    </template>
                </div>
            </div>

            <!-- Reaction Breakdown -->
            <div v-if="!isStatsLoading && aggregatedReactionStats.length > 0" class="mt-6">
                <h5 class="text-lg font-semibold mb-4">Phân tích phản ứng</h5>
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <StatsCard
                        v-for="reaction in aggregatedReactionStats.slice(0, 8)"
                        :key="reaction.label"
                        :title="reaction.label"
                        :value="formatNumber(reaction.count)"
                        description="Lượt phản ứng"
                        color="indigo"
                        :icon="Share"
                    />
                </div>
            </div>


        </div>
    </AppLayout>
</template>
