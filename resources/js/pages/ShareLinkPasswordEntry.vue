<script setup lang="ts">
import { ref } from 'vue';
import { Head, router } from '@inertiajs/vue3';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Lock, Eye, EyeOff, Shield } from 'lucide-vue-next';

interface Props {
    token: string;
    returnUrl: string;
}

const props = defineProps<Props>();

const password = ref('');
const showPassword = ref(false);
const isLoading = ref(false);
const error = ref('');

const verifyPassword = async () => {
    if (!password.value.trim()) {
        error.value = 'Vui lòng nhập mật khẩu.';
        return;
    }
    isLoading.value = true;
    error.value = '';

    try {
        const response = await fetch(`/p/${props.token}/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                password: password.value
            })
        });
        const data = await response.json();

        if (data.success) {
            window.location.href = `/${props.token}/` + 'dashboard';
        }else {
            error.value = data.message;
        }
    } catch (err) {
        error.value = 'Có lỗi xảy ra, vui lòng thử lại sau.';
    } finally {
        isLoading.value = false;
    }
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};
</script>

<template>
    <Head title="Enter Password" />

    <div class="min-h-screen bg-background flex items-center justify-center p-4">
        <Card class="w-full max-w-md">
            <CardHeader class="text-center">
                <div class="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Shield class="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Protected Content</CardTitle>
                <CardDescription>
                    This content is password protected. Please enter the password to continue.
                </CardDescription>
            </CardHeader>

            <CardContent>
                <form @submit.prevent="verifyPassword" class="space-y-4">
                    <div class="space-y-2">
                        <label for="password" class="text-sm font-medium">Password</label>
                        <div class="relative">
                            <Input
                                id="password"
                                v-model="password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="Enter password"
                                class="pr-10"
                                :disabled="isLoading"
                                required
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                @click="togglePasswordVisibility"
                                :disabled="isLoading"
                            >
                                <component
                                    :is="showPassword ? EyeOff : Eye"
                                    class="h-4 w-4 text-muted-foreground"
                                />
                            </Button>
                        </div>
                    </div>

                    <div v-if="error" class="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
                        {{ error }}
                    </div>

                    <Button
                        type="submit"
                        class="w-full"
                        :disabled="isLoading || !password.trim()"
                    >
                        <Lock class="mr-2 h-4 w-4" />
                        {{ isLoading ? 'Verifying...' : 'Access Content' }}
                    </Button>
                </form>

                <div class="mt-4 text-xs text-muted-foreground text-center">
                    Your password will be remembered for this session
                </div>
            </CardContent>
        </Card>
    </div>
</template>
