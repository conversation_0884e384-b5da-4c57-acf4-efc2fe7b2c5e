<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, CheckCircle, Share2, Eye } from 'lucide-vue-next';

interface Props {
    token: string;
}

const props = defineProps<Props>();

const copyShareLink = async () => {
    try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Share link copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy link:', err);
    }
};
</script>

<template>
    <Head title="Shared Content" />

    <div class="min-h-screen bg-background">
        <!-- Header -->
        <div class="border-b bg-card">
            <div class="container mx-auto px-4 py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="p-2 bg-green-100 rounded-lg dark:bg-green-900">
                            <CheckCircle class="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold">Access Granted!</h1>
                            <p class="text-muted-foreground">
                                You have successfully accessed this protected content
                            </p>
                        </div>
                    </div>
                    
                    <Button variant="outline" @click="copyShareLink">
                        <Share2 class="mr-2 h-4 w-4" />
                        Share
                    </Button>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container mx-auto px-4 py-8 space-y-6">
            <!-- Success Message -->
            <Card class="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
                <CardContent class="flex items-center gap-3 p-4">
                    <Shield class="h-5 w-5 text-green-600 dark:text-green-400" />
                    <div>
                        <p class="font-medium text-green-800 dark:text-green-200">Authentication Successful</p>
                        <p class="text-sm text-green-700 dark:text-green-300">
                            Your password has been verified and saved for this session. You won't need to enter it again.
                        </p>
                    </div>
                </CardContent>
            </Card>

            <!-- Demo Content -->
            <Card>
                <CardHeader>
                    <CardTitle>Protected Content</CardTitle>
                    <CardDescription>
                        This is the content that was protected by the middleware
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div class="grid gap-4 md:grid-cols-2">
                            <div class="p-4 border rounded-lg">
                                <h3 class="font-medium mb-2">Share Token</h3>
                                <code class="text-sm bg-muted px-2 py-1 rounded">{{ token }}</code>
                            </div>
                            
                            <div class="p-4 border rounded-lg">
                                <h3 class="font-medium mb-2">Session Status</h3>
                                <Badge variant="default" class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    Authenticated
                                </Badge>
                            </div>
                        </div>

                        <div class="p-4 bg-muted rounded-lg">
                            <h3 class="font-medium mb-2">How it works:</h3>
                            <ol class="text-sm text-muted-foreground space-y-1">
                                <li>1. User visits share link: <code>/share/{{ token }}</code></li>
                                <li>2. Middleware checks if session has authentication for this token</li>
                                <li>3. If not authenticated, redirects to password entry page</li>
                                <li>4. After successful password verification, session is marked as authenticated</li>
                                <li>5. Subsequent visits bypass password entry (until session expires)</li>
                            </ol>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Test Links -->
            <Card>
                <CardHeader>
                    <CardTitle>Test the Middleware</CardTitle>
                    <CardDescription>
                        Try these demo links to test the password protection
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                                <code class="text-sm">/share/demo123</code>
                                <span class="ml-2 text-xs text-muted-foreground">Password: password123</span>
                            </div>
                            <Button variant="outline" size="sm" @click="() => window.open('/share/demo123', '_blank')">
                                <Eye class="mr-2 h-4 w-4" />
                                Test
                            </Button>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                                <code class="text-sm">/share/test456</code>
                                <span class="ml-2 text-xs text-muted-foreground">Password: secret456</span>
                            </div>
                            <Button variant="outline" size="sm" @click="() => window.open('/share/test456', '_blank')">
                                <Eye class="mr-2 h-4 w-4" />
                                Test
                            </Button>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                                <code class="text-sm">/share/sample789</code>
                                <span class="ml-2 text-xs text-muted-foreground">Password: demo789</span>
                            </div>
                            <Button variant="outline" size="sm" @click="() => window.open('/share/sample789', '_blank')">
                                <Eye class="mr-2 h-4 w-4" />
                                Test
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </div>
</template>
