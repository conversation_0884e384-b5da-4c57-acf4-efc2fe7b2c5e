<script setup lang="ts">
import ChannelCard from '@/components/ChannelCard.vue';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem, Channel } from '@/types';
import { Head } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Channels',
        href: '/channel',
    },
];
const token = route().params.token ?? '';

defineProps({ channels: Array, event: String });

</script>

<template>
    <Head title="Các kênh" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
            <!-- Header -->
            <div class="space-y-2">
                <h1 class="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON> kênh</h1>
            </div>

            <!-- Channels Grid -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold">Các kênh có sẵn cho sự vụ: {{event}}</h2>
                    <Badge variant="outline">{{ channels.length }} kênh</Badge>
                </div>

                <Tabs :default-value="channels[0]?.id" class="w-full">
                    <TabsList class="grid w-full grid-cols-4">
                        <TabsTrigger
                            v-for="channel in channels"
                            :key="channel.id"
                            :value="channel.id"
                        >
                            {{ channel.name }}
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent
                        v-for="channel in channels"
                        :key="channel.id"
                        :value="channel.id"
                        class="mt-6"
                    >
                        <div class="grid md:grid-cols-1 lg:grid-cols-1">
                            <ChannelCard :channel="channel" />
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    </AppLayout>
</template>
