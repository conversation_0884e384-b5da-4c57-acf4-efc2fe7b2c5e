<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';
import EnhancedChannelItemsTable from '@/components/EnhancedChannelItemsTable.vue';
import { useChannelData } from '@/composables/useChannelData';
import { computed } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Newspaper, Users, Video, RefreshCw } from 'lucide-vue-next';
import { Link } from '@inertiajs/vue3';

interface Props {
    channelId: string;
}

const props = defineProps<Props>();
const { getChannelById, getChannelItems } = useChannelData();

const channel = computed(() => getChannelById(props.channelId));
const items = computed(() => getChannelItems(props.channelId));

const breadcrumbs = computed((): BreadcrumbItem[] => [
    {
        title: 'Channels',
        href: '/channel',
    },
    {
        title: channel.value?.name || 'Channel',
        href: `/channel/${props.channelId}`,
    },
]);

const channelIcon = computed(() => {
    if (!channel.value) return Newspaper;
    switch (channel.value.type) {
        case 'newspaper':
            return Newspaper;
        case 'social':
            return Users;
        case 'clip':
            return Video;
        default:
            return Newspaper;
    }
});

const channelTypeLabel = computed(() => {
    if (!channel.value) return 'Unknown';
    switch (channel.value.type) {
        case 'newspaper':
            return 'Newspaper';
        case 'social':
            return 'Social Media';
        case 'clip':
            return 'Video Clips';
        default:
            return 'Unknown';
    }
});

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};


</script>

<template>
    <Head :title="channel?.name || 'Channel'" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
            <!-- Channel not found -->
            <div v-if="!channel" class="flex flex-col items-center justify-center py-12">
                <h1 class="text-2xl font-bold text-muted-foreground mb-2">Channel Not Found</h1>
                <p class="text-muted-foreground mb-4">The requested channel could not be found.</p>
                <Button as-child>
                    <Link href="/channel">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back to Channels
                    </Link>
                </Button>
            </div>

            <!-- Channel found -->
            <template v-else>
                <!-- Header -->
                <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div class="space-y-3">
                        <div class="flex items-center gap-2">
                            <Button as-child variant="ghost" size="sm">
                                <Link href="/channel">
                                    <ArrowLeft class="mr-2 h-4 w-4" />
                                    Back to Channels
                                </Link>
                            </Button>
                        </div>

                        <div class="flex items-center gap-3">
                            <component :is="channelIcon" class="h-8 w-8 text-muted-foreground" />
                            <div>
                                <h1 class="text-3xl font-bold tracking-tight">{{ channel.name }}</h1>
                                <div class="flex items-center gap-2 mt-1">
                                    <Badge variant="secondary">{{ channelTypeLabel }}</Badge>
                                    <span class="text-sm text-muted-foreground">
                                        {{ items.length }} items
                                    </span>
                                </div>
                            </div>
                        </div>

                        <p class="text-muted-foreground max-w-2xl">
                            {{ channel.description }}
                        </p>
                    </div>

                    <div class="flex flex-col gap-2 text-sm text-muted-foreground">
                        <div class="flex items-center gap-2">
                            <RefreshCw class="h-4 w-4" />
                            <span>Last updated: {{ formatDate(channel.lastUpdated) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Items Table -->
                <div class="space-y-4">
                    <EnhancedChannelItemsTable :items="items" />
                </div>
            </template>
        </div>
    </AppLayout>
</template>
