version: '3.8'

services:
    app:
        build: .
        container_name: laravel_app_sheet
        restart: unless-stopped
        working_dir: /var/www
        volumes:
            - .:/var/www
        networks:
            - laravel
    nginx:
        image: nginx:latest
        container_name: laravel_nginx_sheet
        restart: unless-stopped
        ports:
            - "8089:80"
        volumes:
            - .:/var/www
            - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
        depends_on:
            - app
        networks:
            - laravel
    mariadb:
        image: mariadb:10.11
        container_name: laravel_mariadb_sheet
        restart: unless-stopped
        ports:
            - "0.0.0.0:3308:3306"
        environment:
            MYSQL_ROOT_PASSWORD: Pewpew@11
        volumes:
            - mariadb_data_sheet:/var/lib/mysql
        networks:
            - laravel

networks:
    laravel:
        driver: bridge
volumes:
    mariadb_data_sheet:
